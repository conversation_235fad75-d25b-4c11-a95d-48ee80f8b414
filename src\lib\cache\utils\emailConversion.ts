/**
 * Email Conversion Utilities
 * Utilities for converting between different email formats for caching
 */

import { InboxEmail } from "@/lib/gmail/types"
import { CachedEmail } from "@/lib/cache/unified/types"

/**
 * Convert InboxEmail to CachedEmail format for unified cache
 */
export function convertInboxEmailToCachedEmail(email: InboxEmail): CachedEmail {
  return {
    id: email.id,
    threadId: email.threadId,
    subject: email.subject,
    from: email.from,
    to: email.to ? [email.to] : [],
    cc: email.cc ? [email.cc] : undefined,
    bcc: email.bcc ? [email.bcc] : undefined,
    date: email.date.toISOString(),
    snippet: email.snippet,
    body: email.body,
    labels: email.labels?.map(label => label.id) || [],
    isRead: email.isRead,
    isStarred: email.isStarred,
    hasAttachments: email.hasAttachments,
    attachments: email.attachments?.map(att => ({
      filename: att.filename,
      mimeType: att.mimeType,
      size: att.size,
      attachmentId: att.attachmentId
    })),
    messageId: email.messageId,
    references: email.references,
    inReplyTo: email.inReplyTo
  }
}

/**
 * Convert CachedEmail back to InboxEmail format
 */
export function convertCachedEmailToInboxEmail(email: CachedEmail): InboxEmail {
  return {
    id: email.id,
    threadId: email.threadId,
    subject: email.subject,
    from: email.from,
    to: email.to?.[0],
    cc: email.cc?.[0],
    bcc: email.bcc?.[0],
    date: new Date(email.date),
    snippet: email.snippet,
    body: email.body,
    labels: email.labels?.map(labelId => ({
      id: labelId,
      name: labelId,
      type: 'system'
    })),
    isRead: email.isRead,
    isStarred: email.isStarred,
    hasAttachments: email.hasAttachments,
    attachments: email.attachments?.map(att => ({
      filename: att.filename,
      mimeType: att.mimeType,
      size: att.size,
      attachmentId: att.attachmentId || att.filename
    })),
    messageId: email.messageId,
    references: email.references,
    inReplyTo: email.inReplyTo
  }
}

/**
 * Generate cache key for email lists
 */
export function generateEmailListCacheKey(
  category: string,
  limit?: number,
  pageToken?: string,
  labelId?: string,
  dateFrom?: string,
  dateTo?: string
): string {
  return `${category}_${limit || 20}_${pageToken || 'first'}_${labelId || 'all'}_${dateFrom || 'all'}_${dateTo || 'all'}`
}

/**
 * Generate cache key for individual emails
 */
export function generateEmailCacheKey(emailId: string): string {
  return `email_detail_${emailId}`
}

/**
 * Generate cache key for threads
 */
export function generateThreadCacheKey(threadId: string): string {
  return `thread_${threadId}`
}
