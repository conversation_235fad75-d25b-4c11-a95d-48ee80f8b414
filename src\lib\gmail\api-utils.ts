import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export interface AuthenticatedUser {
  id: string
  email: string
  gmailRefreshToken: string | null
  gmailConnected: boolean
  dailySendLimit: number
  dailySendCount: number
  lastSendReset: Date | null
}

export interface GmailApiContext {
  user: AuthenticatedUser
  url: URL
}

/**
 * Standard authentication and Gmail connection check for all routes
 */
export async function authenticateGmailUser(request: NextRequest): Promise<{
  success: true
  data: GmailApiContext
} | {
  success: false
  response: NextResponse
}> {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return {
        success: false,
        response: NextResponse.json({ error: "Unauthorized" }, { status: 401 })
      }
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        gmailRefreshToken: true,
        gmailConnected: true,
        dailySendLimit: true,
        dailySendCount: true,
        lastSendReset: true
      }
    })

    if (!user) {
      return {
        success: false,
        response: NextResponse.json({ error: "User not found" }, { status: 404 })
      }
    }

    // Check if user has Gmail connected
    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return {
        success: false,
        response: NextResponse.json({ 
          error: "Gmail account not connected. Please connect your Gmail account first." 
        }, { status: 400 })
      }
    }

    return {
      success: true,
      data: {
        user: user as AuthenticatedUser,
        url: new URL(request.url)
      }
    }
  } catch (error) {
    console.error("Authentication error:", error)
    return {
      success: false,
      response: NextResponse.json(
        { error: "Authentication failed" },
        { status: 500 }
      )
    }
  }
}

/**
 * Standard query parameter parsing for email list endpoints
 */
export function parseEmailListParams(url: URL) {
  return {
    limit: parseInt(url.searchParams.get('limit') || '50'),
    page: parseInt(url.searchParams.get('page') || '1'),
    pageToken: url.searchParams.get('pageToken') || undefined,
    labelId: url.searchParams.get('labelId') || undefined,
    dateFrom: url.searchParams.get('dateFrom') ? new Date(url.searchParams.get('dateFrom')!) : undefined,
    dateTo: url.searchParams.get('dateTo') ? new Date(url.searchParams.get('dateTo')!) : undefined
  }
}

/**
 * Standard success response formatter
 */
export function createSuccessResponse(data: any) {
  return NextResponse.json({
    success: true,
    ...data
  })
}

// Import consolidated error handling
import { createApiErrorResponse } from '@/lib/validation/utils'

/**
 * Standard error response handler with Gmail-specific error handling
 * @deprecated Use createApiErrorResponse from validation/utils instead
 */
export function createErrorResponse(error: any, operation?: string): NextResponse {
  const { response } = createApiErrorResponse(error, {
    operation: operation ? `Gmail ${operation}` : 'Gmail API',
    includeReconnectionFlag: true
  })

  return new NextResponse(response.body, {
    status: response.status,
    headers: response.headers
  })
}

/**
 * Standard wrapper for Gmail API route handlers
 */
export async function withGmailAuth<T>(
  request: NextRequest,
  handler: (context: GmailApiContext) => Promise<T>
): Promise<NextResponse> {
  try {
    const authResult = await authenticateGmailUser(request)
    
    if (!authResult.success) {
      return authResult.response
    }
    
    const result = await handler(authResult.data)
    return createSuccessResponse(result)
  } catch (error) {
    return createErrorResponse(error)
  }
}

/**
 * Standard email operation result formatter
 */
export function formatEmailOperationResult(
  success: boolean,
  message: string,
  additionalData?: any
) {
  if (success) {
    return {
      success: true,
      message,
      ...additionalData
    }
  } else {
    throw new Error(`Failed to ${message.toLowerCase()}`)
  }
}

/**
 * Validate required JSON body fields
 */
export function validateRequiredFields(
  body: any,
  requiredFields: string[]
): { isValid: boolean; missingFields?: string[] } {
  const missingFields = requiredFields.filter(field => !body[field])
  
  return {
    isValid: missingFields.length === 0,
    missingFields: missingFields.length > 0 ? missingFields : undefined
  }
}

/**
 * Standard bulk operation response formatter
 */
export function formatBulkOperationResult(
  totalItems: number,
  successCount: number,
  failedCount: number,
  operation: string
) {
  return {
    success: true,
    message: `Successfully ${operation} ${successCount} out of ${totalItems} email(s)`,
    stats: {
      total: totalItems,
      success: successCount,
      failed: failedCount
    }
  }
} 