/**
 * Unified Cache System Types
 * Comprehensive type definitions for centralized caching across all application modules
 */

import { z } from 'zod'

// ============================================================================
// BASE CACHE TYPES
// ============================================================================

export interface BaseCacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  key: string
  userId?: string
}

export interface CacheOperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
  fromCache: boolean
  timestamp: number
}

export interface InvalidationOptions {
  module?: string
  userId?: string
  pattern?: string
  force?: boolean
}

// ============================================================================
// GMAIL CACHE TYPES WITH ZOD VALIDATION
// ============================================================================

export const cachedEmailSchema = z.object({
  id: z.string(),
  threadId: z.string(),
  subject: z.string(),
  from: z.string(),
  to: z.array(z.string()),
  cc: z.array(z.string()).optional(),
  bcc: z.array(z.string()).optional(),
  date: z.string(),
  snippet: z.string(),
  body: z.string().optional(),
  labels: z.array(z.string()),
  isRead: z.boolean(),
  isStarred: z.boolean(),
  hasAttachments: z.boolean(),
  attachments: z.array(z.object({
    filename: z.string(),
    mimeType: z.string(),
    size: z.number(),
    attachmentId: z.string().optional()
  })).optional(),
  messageId: z.string().optional(),
  references: z.string().optional(),
  inReplyTo: z.string().optional()
})

export type CachedEmail = z.infer<typeof cachedEmailSchema>

export const cachedEmailListSchema = z.object({
  emails: z.array(cachedEmailSchema),
  nextPageToken: z.string().optional(),
  totalCount: z.number(),
  query: z.string().optional(),
  category: z.string().optional(),
  timestamp: z.number()
})

export type CachedEmailList = z.infer<typeof cachedEmailListSchema>

export const cachedThreadSchema = z.object({
  id: z.string(),
  messages: z.array(cachedEmailSchema),
  subject: z.string(),
  participants: z.array(z.string()),
  lastMessageDate: z.string(),
  messageCount: z.number(),
  snippet: z.string().optional(),
  historyId: z.string().optional()
})

export type CachedThread = z.infer<typeof cachedThreadSchema>

// ============================================================================
// CALENDAR CACHE TYPES WITH ZOD VALIDATION
// ============================================================================

export const cachedCalendarEventSchema = z.object({
  id: z.string(),
  summary: z.string(),
  description: z.string().optional(),
  start: z.object({
    dateTime: z.string().optional(),
    date: z.string().optional(),
    timeZone: z.string().optional()
  }),
  end: z.object({
    dateTime: z.string().optional(),
    date: z.string().optional(),
    timeZone: z.string().optional()
  }),
  attendees: z.array(z.object({
    email: z.string(),
    displayName: z.string().optional(),
    responseStatus: z.enum(['needsAction', 'declined', 'tentative', 'accepted']).optional(),
    organizer: z.boolean().optional()
  })).optional(),
  location: z.string().optional(),
  status: z.enum(['confirmed', 'tentative', 'cancelled']),
  created: z.string(),
  updated: z.string(),
  creator: z.object({
    email: z.string(),
    displayName: z.string().optional()
  }).optional(),
  organizer: z.object({
    email: z.string(),
    displayName: z.string().optional()
  }).optional(),
  recurringEventId: z.string().optional(),
  recurrence: z.array(z.string()).optional(),
  conferenceData: z.object({
    entryPoints: z.array(z.object({
      entryPointType: z.string(),
      uri: z.string(),
      label: z.string().optional()
    })).optional(),
    conferenceSolution: z.object({
      name: z.string(),
      iconUri: z.string().optional()
    }).optional(),
    conferenceId: z.string().optional()
  }).optional(),
  reminders: z.object({
    useDefault: z.boolean(),
    overrides: z.array(z.object({
      method: z.enum(['email', 'popup']),
      minutes: z.number()
    })).optional()
  }).optional()
})

export type CachedCalendarEvent = z.infer<typeof cachedCalendarEventSchema>

export const cachedCalendarListSchema = z.object({
  events: z.array(cachedCalendarEventSchema),
  nextPageToken: z.string().optional(),
  timeMin: z.string().optional(),
  timeMax: z.string().optional(),
  totalCount: z.number(),
  calendarId: z.string().optional(),
  timestamp: z.number()
})

export type CachedCalendarList = z.infer<typeof cachedCalendarListSchema>



// ============================================================================
// AI AGENT CACHE TYPES WITH ZOD VALIDATION
// ============================================================================

export const cachedAiConversationSchema = z.object({
  id: z.string(),
  userId: z.string(),
  title: z.string(),
  messages: z.array(z.object({
    id: z.string(),
    role: z.enum(['user', 'assistant']),
    content: z.string(),
    timestamp: z.string(),
    metadata: z.object({
      actionType: z.string().optional(),
      actionResult: z.any().optional()
    }).optional()
  })),
  context: z.object({
    userBehavior: z.any().optional(),
    knowledgeGraph: z.any().optional(),
    preferences: z.any().optional()
  }),
  created: z.string(),
  updated: z.string(),
  isActive: z.boolean().default(true),
  messageCount: z.number()
})

export type CachedAiConversation = z.infer<typeof cachedAiConversationSchema>

export const cachedUserBehaviorSchema = z.object({
  userId: z.string(),
  emailPatterns: z.object({
    frequentContacts: z.array(z.string()),
    commonSubjects: z.array(z.string()),
    sendingTimes: z.array(z.string()),
    responsePatterns: z.any()
  }),
  calendarPatterns: z.object({
    meetingFrequency: z.any(),
    preferredTimes: z.array(z.string()),
    commonLocations: z.array(z.string())
  }),
  preferences: z.object({
    timezone: z.string(),
    workingHours: z.object({
      start: z.string(),
      end: z.string()
    }),
    communicationStyle: z.string()
  }),
  lastAnalyzed: z.string()
})

export type CachedUserBehavior = z.infer<typeof cachedUserBehaviorSchema>

export const cachedKnowledgeEntrySchema = z.object({
  id: z.string(),
  userId: z.string(),
  type: z.enum(['email', 'calendar', 'meet', 'general']),
  content: z.string(),
  metadata: z.any(),
  relevanceScore: z.number(),
  created: z.string(),
  updated: z.string()
})

export type CachedKnowledgeEntry = z.infer<typeof cachedKnowledgeEntrySchema>

// ============================================================================
// CACHE CONFIGURATION TYPES
// ============================================================================

export interface CacheConfig {
  ttl: number // Time to live in milliseconds
  maxSize: number // Maximum number of entries
  cleanupInterval: number // Cleanup interval in milliseconds
}

export interface ModuleCacheConfig {
  gmail: CacheConfig
  calendar: CacheConfig
  meet: CacheConfig
  aiAgent: CacheConfig
}

export interface CacheStats {
  totalEntries: number
  memoryUsage: number
  hitRate: number
  missRate: number
  lastCleanup: number
  moduleStats: {
    [module: string]: {
      entries: number
      hits: number
      misses: number
    }
  }
}

// ============================================================================
// CACHE KEY PATTERNS AND CONSTANTS
// ============================================================================

// Export cache key patterns
export const CACHE_KEY_PATTERNS = {
  GMAIL: {
    EMAIL_LIST: 'gmail:emails:list',
    EMAIL_DETAIL: 'gmail:email:detail',
    THREAD: 'gmail:thread',
    LABELS: 'gmail:labels',
    DRAFTS: 'gmail:drafts'
  },
  CALENDAR: {
    EVENT_LIST: 'calendar:events:list',
    EVENT_DETAIL: 'calendar:event:detail',
    CALENDARS: 'calendar:calendars',
    AVAILABILITY: 'calendar:availability'
  },
  MEET: {
    SPACES: 'meet:spaces',
    CONFERENCES: 'meet:conferences',
    PARTICIPANTS: 'meet:participants'
  },
  AI_AGENT: {
    CONVERSATIONS: 'ai:conversations',
    USER_BEHAVIOR: 'ai:behavior',
    KNOWLEDGE_GRAPH: 'ai:knowledge'
  }
} as const

// Default cache configurations
export const DEFAULT_CACHE_CONFIG: ModuleCacheConfig = {
  gmail: {
    ttl: 2 * 60 * 1000, // 2 minutes
    maxSize: 1000,
    cleanupInterval: 5 * 60 * 1000 // 5 minutes
  },
  calendar: {
    ttl: 15 * 60 * 1000, // 15 minutes
    maxSize: 500,
    cleanupInterval: 10 * 60 * 1000 // 10 minutes
  },
  meet: {
    ttl: 10 * 60 * 1000, // 10 minutes
    maxSize: 200,
    cleanupInterval: 15 * 60 * 1000 // 15 minutes
  },
  aiAgent: {
    ttl: 30 * 60 * 1000, // 30 minutes
    maxSize: 100,
    cleanupInterval: 20 * 60 * 1000 // 20 minutes
  }
}
