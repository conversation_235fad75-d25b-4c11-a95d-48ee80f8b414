"use client"

import { useSession, signOut } from "next-auth/react"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { AppSidebar } from "@/components/dashboard/sidebar"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { UnifiedCacheProvider } from "@/contexts/cache"
import { AppPreloader } from "@/components/preloader/AppPreloader"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/auth/signin")
  }, [session, status, router])

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <UnifiedCacheProvider
      userId={session?.user?.id}
      autoPreload={false}
      config={{
        gmail: { ttl: 2 * 60 * 1000, maxSize: 1000 }, // 2 minutes
        calendar: { ttl: 15 * 60 * 1000, maxSize: 500 }, // 15 minutes
        meet: { ttl: 10 * 60 * 1000, maxSize: 200 },
        aiAgent: { ttl: 30 * 60 * 1000, maxSize: 100 }
      }}
    >
      <SidebarProvider>
        <AppSidebar
          gmailConnected={session?.user?.email ? true : false}
          user={session?.user?.email ? { ...session.user, id: session.user.email, email: session.user.email } : undefined}
          onSignOut={handleSignOut}
        />
        <SidebarInset>
          <div className="flex flex-1 flex-col p-0">
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>

      {/* Single unified preloader - TEMPORARILY DISABLED FOR DEBUGGING */}
      {/* <AppPreloader
        showProgress={true}
        showMetrics={false}
        priority="high"
        autoStart={true}
        onComplete={(summary) => {
          console.log('🎉 [UNIFIED PRELOADER] App preloading completed with summary:', summary)
        }}
      /> */}
    </UnifiedCacheProvider>
  )
}
