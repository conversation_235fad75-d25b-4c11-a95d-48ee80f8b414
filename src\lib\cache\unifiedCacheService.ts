/**
 * Unified Cache Service
 * Single point of access for all caching operations across the application
 */

import { cacheManager } from './unified/cacheManager'
import {
  CachedEmail,
  CachedEmailList,
  CachedThread,
  CachedCalendarEvent,
  CachedCalendarList,
  CachedAiConversation,
  CachedUserBehavior,
  CacheOperationResult,
  InvalidationOptions,
  CacheStats,
  cachedEmailSchema,
  cachedEmailListSchema,
  cachedThreadSchema,
  cachedCalendarEventSchema,
  cachedCalendarListSchema,
  cachedAiConversationSchema,
  cachedUserBehaviorSchema
} from './unified/types'

/**
 * Unified Cache Service Class
 * Provides high-level caching operations with validation and error handling
 */
class UnifiedCacheService {
  // ============================================================================
  // GMAIL CACHE OPERATIONS
  // ============================================================================

  /**
   * Cache email list with validation
   */
  cacheEmailList(
    userId: string,
    emails: CachedEmailList,
    category: string = 'inbox',
    query?: string
  ): CacheOperationResult<CachedEmailList> {
    try {
      const validatedData = cachedEmailListSchema.parse(emails)
      const key = `emails:list:${category}:${query || 'all'}`
      return cacheManager.set('gmail', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached email list
   */
  getCachedEmailList(
    userId: string,
    category: string = 'inbox',
    query?: string
  ): CacheOperationResult<CachedEmailList> {
    const key = `emails:list:${category}:${query || 'all'}`
    return cacheManager.get<CachedEmailList>('gmail', key, userId)
  }

  /**
   * Cache individual email with validation
   */
  cacheEmail(
    userId: string,
    email: CachedEmail
  ): CacheOperationResult<CachedEmail> {
    try {
      const validatedData = cachedEmailSchema.parse(email)
      const key = `email:detail:${email.id}`
      return cacheManager.set('gmail', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached email
   */
  getCachedEmail(
    userId: string,
    emailId: string
  ): CacheOperationResult<CachedEmail> {
    const key = `email:detail:${emailId}`
    return cacheManager.get<CachedEmail>('gmail', key, userId)
  }

  /**
   * Cache email thread with validation
   */
  cacheThread(
    userId: string,
    thread: CachedThread
  ): CacheOperationResult<CachedThread> {
    try {
      const validatedData = cachedThreadSchema.parse(thread)
      const key = `thread:${thread.id}`
      return cacheManager.set('gmail', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached thread
   */
  getCachedThread(
    userId: string,
    threadId: string
  ): CacheOperationResult<CachedThread> {
    const key = `thread:${threadId}`
    return cacheManager.get<CachedThread>('gmail', key, userId)
  }

  // ============================================================================
  // CALENDAR CACHE OPERATIONS
  // ============================================================================

  /**
   * Cache calendar events with validation
   */
  cacheCalendarEvents(
    userId: string,
    events: CachedCalendarList,
    timeMin?: string,
    timeMax?: string,
    calendarId?: string
  ): CacheOperationResult<CachedCalendarList> {
    try {
      const validatedData = cachedCalendarListSchema.parse(events)
      const key = `events:list:${calendarId || 'primary'}:${timeMin || 'all'}:${timeMax || 'all'}`
      return cacheManager.set('calendar', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached calendar events
   */
  getCachedCalendarEvents(
    userId: string,
    timeMin?: string,
    timeMax?: string,
    calendarId?: string
  ): CacheOperationResult<CachedCalendarList> {
    const key = `events:list:${calendarId || 'primary'}:${timeMin || 'all'}:${timeMax || 'all'}`
    return cacheManager.get<CachedCalendarList>('calendar', key, userId)
  }

  /**
   * Cache individual calendar event
   */
  cacheCalendarEvent(
    userId: string,
    event: CachedCalendarEvent
  ): CacheOperationResult<CachedCalendarEvent> {
    try {
      const validatedData = cachedCalendarEventSchema.parse(event)
      const key = `event:detail:${event.id}`
      return cacheManager.set('calendar', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached calendar event
   */
  getCachedCalendarEvent(
    userId: string,
    eventId: string
  ): CacheOperationResult<CachedCalendarEvent> {
    const key = `event:detail:${eventId}`
    return cacheManager.get<CachedCalendarEvent>('calendar', key, userId)
  }

  /**
   * Get cached calendar list
   */
  getCachedCalendarList(userId: string): CacheOperationResult<any> {
    const key = 'calendars:list'
    return cacheManager.get('calendar', key, userId)
  }

  /**
   * Cache calendar list
   */
  cacheCalendarList(userId: string, calendars: any[]): CacheOperationResult<any> {
    const key = 'calendars:list'
    return cacheManager.set('calendar', key, calendars, userId)
  }

  /**
   * Get cached calendar colors
   */
  getCachedCalendarColors(userId: string): CacheOperationResult<any> {
    const key = 'colors'
    return cacheManager.get('calendar', key, userId)
  }

  /**
   * Cache calendar colors
   */
  cacheCalendarColors(userId: string, colors: any): CacheOperationResult<any> {
    const key = 'colors'
    return cacheManager.set('calendar', key, colors, userId)
  }

  /**
   * Get cached calendar settings
   */
  getCachedCalendarSettings(userId: string): CacheOperationResult<any> {
    const key = 'settings'
    return cacheManager.get('calendar', key, userId)
  }

  /**
   * Cache calendar settings
   */
  cacheCalendarSettings(userId: string, settings: any): CacheOperationResult<any> {
    const key = 'settings'
    return cacheManager.set('calendar', key, settings, userId)
  }

  /**
   * Cache free/busy data
   */
  cacheFreeBusy(
    userId: string,
    freeBusyData: any,
    timeMin: string,
    timeMax: string
  ): CacheOperationResult<any> {
    const key = `freebusy:${timeMin}:${timeMax}`
    return cacheManager.set('calendar', key, freeBusyData, userId)
  }

  /**
   * Get cached free/busy data
   */
  getCachedFreeBusy(
    userId: string,
    timeMin: string,
    timeMax: string
  ): CacheOperationResult<any> {
    const key = `freebusy:${timeMin}:${timeMax}`
    return cacheManager.get('calendar', key, userId)
  }
  // ============================================================================
  // AI AGENT CACHE OPERATIONS
  // ============================================================================

  /**
   * Cache AI conversation
   */
  cacheConversation(
    userId: string,
    conversation: CachedAiConversation
  ): CacheOperationResult<CachedAiConversation> {
    try {
      const validatedData = cachedAiConversationSchema.parse(conversation)
      const key = `conversation:${conversation.id}`
      return cacheManager.set('aiAgent', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached conversation
   */
  getCachedConversation(
    userId: string,
    conversationId: string
  ): CacheOperationResult<CachedAiConversation> {
    const key = `conversation:${conversationId}`
    return cacheManager.get<CachedAiConversation>('aiAgent', key, userId)
  }

  /**
   * Cache user behavior data
   */
  cacheUserBehavior(
    userId: string,
    behavior: CachedUserBehavior
  ): CacheOperationResult<CachedUserBehavior> {
    try {
      const validatedData = cachedUserBehaviorSchema.parse(behavior)
      const key = `behavior:${userId}`
      return cacheManager.set('aiAgent', key, validatedData, userId)
    } catch (error) {
      return {
        success: false,
        error: `Validation failed: ${error}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }
  }

  /**
   * Get cached user behavior
   */
  getCachedUserBehavior(
    userId: string
  ): CacheOperationResult<CachedUserBehavior> {
    const key = `behavior:${userId}`
    return cacheManager.get<CachedUserBehavior>('aiAgent', key, userId)
  }

  // ============================================================================
  // GENERIC CACHE OPERATIONS
  // ============================================================================

  /**
   * Generic cache data method
   */
  cacheData<T = any>(
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent',
    key: string,
    data: T,
    userId?: string
  ): CacheOperationResult<T> {
    return cacheManager.set(module, key, data, userId)
  }

  /**
   * Generic get cached data method
   */
  getCachedData<T = any>(
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent',
    key: string,
    userId?: string
  ): CacheOperationResult<T> {
    return cacheManager.get<T>(module, key, userId)
  }

  // ============================================================================
  // CACHE MANAGEMENT OPERATIONS
  // ============================================================================

  /**
   * Invalidate cache entries
   */
  invalidateCache(options: InvalidationOptions): number {
    return cacheManager.invalidate(options)
  }

  /**
   * Clear all cache for a user
   */
  clearUserCache(userId: string): number {
    return cacheManager.invalidate({ userId, force: true })
  }

  /**
   * Clear all cache for a module
   */
  clearModuleCache(module: 'gmail' | 'calendar' | 'meet' | 'aiAgent'): number {
    return cacheManager.invalidate({ module, force: true })
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return cacheManager.getStats()
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    return cacheManager.cleanup()
  }

  /**
   * Check if cache entry exists and is valid
   */
  has(module: string, key: string, userId?: string): boolean {
    const result = cacheManager.get(module, key, userId)
    return result.success
  }

  /**
   * Preload common data for a user
   */
  async preloadUserData(userId: string): Promise<void> {
    try {
      console.log(`🚀 Starting cache preload for user: ${userId}`)

      // This would typically trigger API calls to populate cache
      // Implementation depends on specific API endpoints

      console.log(`✅ Cache preload completed for user: ${userId}`)
    } catch (error) {
      console.error(`❌ Cache preload failed for user ${userId}:`, error)
    }
  }

  /**
   * Update configuration
   */
  updateConfig(config: any): void {
    cacheManager.updateConfig(config)
  }
}

// Create and export singleton instance
export const unifiedCacheService = new UnifiedCacheService()

// Export the class for testing or custom instances
export { UnifiedCacheService }

// Export types for external use
export type {
  CachedEmail,
  CachedEmailList,
  CachedThread,
  CachedCalendarEvent,
  CachedCalendarList,
  CachedAiConversation,
  CachedUserBehavior,
  CacheOperationResult,
  InvalidationOptions,
  CacheStats
}
