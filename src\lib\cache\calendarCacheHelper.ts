// Calendar Cache Helper
// Provides calendar-specific caching functionality using the unified cache system

import { unifiedCacheService } from '@/contexts/cache'
import { CachedCalendarEvent, CachedCalendarList } from '@/contexts/cache/types'

export class CalendarCacheHelper {
  private static readonly CACHE_DURATION = 10 * 60 * 1000 // 10 minutes for calendar data

  /**
   * Cache calendar events for a specific time range
   */
  static cacheCalendarEvents(
    key: string,
    events: any[],
    timeMin?: string,
    timeMax?: string,
    calendarId?: string
  ): void {
    const cachedList: CachedCalendarList = {
      events: events.map(event => this.transformToCache(event)),
      totalCount: events.length,
      timestamp: Date.now(),
      timeMin,
      timeMax,
      calendarId
    }

    unifiedCacheService.cacheData('calendar', key, cachedList)
    console.log(`📅 Cached ${events.length} calendar events for key: ${key}`)
  }

  /**
   * Get cached calendar events
   */
  static getCachedCalendarEvents(key: string): CachedCalendarList | null {
    const result = unifiedCacheService.getCachedData<CachedCalendarList>('calendar', key)
    const cached = result.success ? result.data || null : null
    if (cached) {
      console.log(`📅 Retrieved ${cached.events.length} cached calendar events for key: ${key}`)
    }
    return cached
  }

  /**
   * Cache a specific calendar event
   */
  static cacheCalendarEvent(eventId: string, event: any): void {
    const cachedEvent = this.transformToCache(event)
    unifiedCacheService.cacheData('calendar', `eventDetails.${eventId}`, cachedEvent)
    console.log(`📅 Cached calendar event: ${eventId}`)
  }

  /**
   * Get a cached calendar event
   */
  static getCachedCalendarEvent(eventId: string): CachedCalendarEvent | null {
    const result = unifiedCacheService.getCachedData<CachedCalendarEvent>('calendar', `eventDetails.${eventId}`)
    const cached = result.success ? result.data || null : null
    if (cached) {
      console.log(`📅 Retrieved cached calendar event: ${eventId}`)
    }
    return cached
  }

  /**
   * Cache free/busy information
   */
  static cacheFreeBusy(
    key: string,
    freeBusyData: any,
    timeMin: string,
    timeMax: string
  ): void {
    const cacheData = {
      timeMin,
      timeMax,
      calendars: freeBusyData.calendars || {}
    }

    unifiedCacheService.cacheData('calendar', `freeBusy.${key}`, cacheData)
    console.log(`📅 Cached free/busy data for key: ${key}`)
  }

  /**
   * Get cached free/busy information
   */
  static getCachedFreeBusy(key: string): any | null {
    const result = unifiedCacheService.getCachedData('calendar', `freeBusy.${key}`)
    const cached = result.success ? result.data || null : null
    if (cached) {
      console.log(`📅 Retrieved cached free/busy data for key: ${key}`)
    }
    return cached
  }

  /**
   * Invalidate calendar cache for specific patterns
   */
  static invalidateCalendarCache(pattern?: string): number {
    if (pattern) {
      return unifiedCacheService.invalidateCache({
        module: 'calendar',
        pattern
      })
    } else {
      return unifiedCacheService.clearModuleCache('calendar')
    }
  }

  /**
   * Get cache keys for different time ranges
   */
  static getCacheKeys() {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)
    const nextWeek = new Date(today)
    nextWeek.setDate(today.getDate() + 7)
    const nextMonth = new Date(today)
    nextMonth.setMonth(today.getMonth() + 1)

    return {
      TODAY: `events_${today.toISOString().split('T')[0]}`,
      TOMORROW: `events_${tomorrow.toISOString().split('T')[0]}`,
      WEEK: `events_week_${today.toISOString().split('T')[0]}`,
      MONTH: `events_month_${today.getFullYear()}_${today.getMonth() + 1}`,
      UPCOMING: 'events_upcoming'
    }
  }

  /**
   * Preload common calendar data
   */
  static async preloadCalendarData(userId: string): Promise<void> {
    try {
      console.log('📅 Starting calendar data preloading...')
      
      const today = new Date()
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
      const keys = this.getCacheKeys()

      // Preload upcoming events for the next week
      const upcomingResponse = await fetch(
        `/api/calendar/events?timeMin=${today.toISOString()}&timeMax=${nextWeek.toISOString()}&maxResults=50`
      )

      if (upcomingResponse.ok) {
        const data = await upcomingResponse.json()
        if (data.events) {
          this.cacheCalendarEvents(
            keys.UPCOMING,
            data.events,
            today.toISOString(),
            nextWeek.toISOString()
          )
        }
      }

      // Preload today's events
      const todayEnd = new Date(today)
      todayEnd.setHours(23, 59, 59, 999)
      
      const todayResponse = await fetch(
        `/api/calendar/events?timeMin=${today.toISOString()}&timeMax=${todayEnd.toISOString()}&maxResults=20`
      )

      if (todayResponse.ok) {
        const data = await todayResponse.json()
        if (data.events) {
          this.cacheCalendarEvents(
            keys.TODAY,
            data.events,
            today.toISOString(),
            todayEnd.toISOString()
          )
        }
      }

      console.log('✅ Calendar data preloading completed')
    } catch (error) {
      console.error('❌ Error preloading calendar data:', error)
    }
  }

  /**
   * Transform Google Calendar event to cached format
   */
  private static transformToCache(event: any): CachedCalendarEvent {
    return {
      id: event.id,
      summary: event.summary || 'Untitled Event',
      description: event.description,
      start: {
        dateTime: event.start?.dateTime,
        date: event.start?.date,
        timeZone: event.start?.timeZone
      },
      end: {
        dateTime: event.end?.dateTime,
        date: event.end?.date,
        timeZone: event.end?.timeZone
      },
      attendees: event.attendees?.map((attendee: any) => ({
        email: attendee.email,
        displayName: attendee.displayName,
        responseStatus: attendee.responseStatus,
        organizer: attendee.organizer
      })),
      location: event.location,
      conferenceData: event.conferenceData ? {
        conferenceId: event.conferenceData.conferenceId,
        conferenceSolution: {
          name: event.conferenceData.conferenceSolution?.name || 'Google Meet',
          iconUri: event.conferenceData.conferenceSolution?.iconUri
        },
        entryPoints: event.conferenceData.entryPoints || []
      } : undefined,
      recurrence: event.recurrence,
      reminders: event.reminders,
      status: event.status,
      created: event.created,
      updated: event.updated,
      creator: event.creator ? {
        email: event.creator.email,
        displayName: event.creator.displayName
      } : undefined,
      organizer: event.organizer ? {
        email: event.organizer.email,
        displayName: event.organizer.displayName
      } : undefined
    }
  }

  /**
   * Get calendar statistics from cache
   */
  static getCalendarCacheStats(): {
    totalEvents: number
    totalLists: number
    cacheKeys: string[]
  } {
    // This is a simplified version - in a real implementation,
    // you'd traverse the calendar namespace to get detailed stats
    return {
      totalEvents: 0, // Would count individual events
      totalLists: 0,  // Would count event lists
      cacheKeys: []   // Would list all cache keys
    }
  }
}

// Export utility functions for easy use
export const CalendarCache = {
  // Event caching
  cacheEvents: CalendarCacheHelper.cacheCalendarEvents.bind(CalendarCacheHelper),
  getEvents: CalendarCacheHelper.getCachedCalendarEvents.bind(CalendarCacheHelper),
  cacheEvent: CalendarCacheHelper.cacheCalendarEvent.bind(CalendarCacheHelper),
  getEvent: CalendarCacheHelper.getCachedCalendarEvent.bind(CalendarCacheHelper),
  
  // Free/busy caching
  cacheFreeBusy: CalendarCacheHelper.cacheFreeBusy.bind(CalendarCacheHelper),
  getFreeBusy: CalendarCacheHelper.getCachedFreeBusy.bind(CalendarCacheHelper),
  
  // Cache management
  invalidate: CalendarCacheHelper.invalidateCalendarCache.bind(CalendarCacheHelper),
  preload: CalendarCacheHelper.preloadCalendarData.bind(CalendarCacheHelper),
  getStats: CalendarCacheHelper.getCalendarCacheStats.bind(CalendarCacheHelper),
  
  // Utility
  getCacheKeys: CalendarCacheHelper.getCacheKeys.bind(CalendarCacheHelper)
}
