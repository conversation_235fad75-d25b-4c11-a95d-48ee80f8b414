import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'

export interface PreloadOptions {
  userId: string
  priority?: 'high' | 'medium' | 'low'
  modules?: {
    gmail?: boolean
    calendar?: boolean
    aiAgent?: boolean
  }
  timeRanges?: {
    calendar?: {
      pastDays?: number
      futureDays?: number
    }
    gmail?: {
      maxEmails?: number
      includeDrafts?: boolean
      includeSent?: boolean
    }
  }
}

export interface PreloadResult {
  success: boolean
  module: string
  dataType: string
  cached: boolean
  loadTime: number
  error?: string
}

export interface PreloadSummary {
  totalTime: number
  successCount: number
  errorCount: number
  results: PreloadResult[]
  cacheHitRate: number
}

class AppPreloader {
  private isPreloading = false
  private preloadQueue: Map<string, Promise<PreloadResult[]>> = new Map()

  /**
   * Main preload function - loads all critical app data
   */
  async preloadAllAppData(options: PreloadOptions): Promise<PreloadSummary> {
    const startTime = Date.now()
    const { userId, modules = {}, priority = 'high' } = options

    // Prevent duplicate preloading for same user
    const cacheKey = `preload:${userId}`
    if (this.preloadQueue.has(cacheKey)) {
      console.log('🔄 Preloading already in progress for user:', userId)
      return await this.preloadQueue.get(cacheKey)!.then(() => ({ 
        totalTime: 0, 
        successCount: 0, 
        errorCount: 0, 
        results: [], 
        cacheHitRate: 0 
      }))
    }

    this.isPreloading = true
    console.log('🚀 [PRELOADER] Starting comprehensive app preloading for user:', userId)
    console.log('🚀 [PRELOADER] Modules to preload:', modules)
    console.log('🚀 [PRELOADER] Priority:', priority)
    console.log('🚀 [PRELOADER] Time ranges:', options.timeRanges)

    const preloadPromise = this.executePreloading(options)
    this.preloadQueue.set(cacheKey, preloadPromise)

    try {
      const results = await preloadPromise
      const totalTime = Date.now() - startTime
      
      const summary: PreloadSummary = {
        totalTime,
        successCount: results.filter(r => r.success).length,
        errorCount: results.filter(r => !r.success).length,
        results,
        cacheHitRate: results.filter(r => r.cached).length / results.length
      }

      console.log('✅ App preloading completed:', {
        totalTime: `${totalTime}ms`,
        success: summary.successCount,
        errors: summary.errorCount,
        cacheHitRate: `${(summary.cacheHitRate * 100).toFixed(1)}%`
      })

      return summary
    } finally {
      this.isPreloading = false
      this.preloadQueue.delete(cacheKey)
    }
  }

  /**
   * Execute preloading based on priority
   */
  private async executePreloading(options: PreloadOptions): Promise<PreloadResult[]> {
    const { priority = 'high', modules = {} } = options
    const results: PreloadResult[] = []

    // Define preloading strategies based on priority
    const strategies = {
      high: {
        // Critical data - load immediately and in parallel
        immediate: ['gmail:inbox', 'calendar:today', 'calendar:thisWeek'],
        parallel: ['gmail:sent', 'gmail:drafts', 'calendar:thisMonth'],
        background: ['calendar:colors', 'gmail:labels']
      },
      medium: {
        immediate: ['gmail:inbox', 'calendar:today'],
        parallel: ['calendar:thisWeek'],
        background: ['gmail:sent', 'gmail:drafts', 'calendar:thisMonth']
      },
      low: {
        immediate: ['gmail:inbox'],
        parallel: ['calendar:today'],
        background: ['calendar:thisWeek']
      }
    }

    const strategy = strategies[priority]

    // Phase 1: Critical immediate data
    console.log('📋 Phase 1: Loading critical immediate data')
    const immediateResults = await this.loadDataItems(options, strategy.immediate)
    results.push(...immediateResults)

    // Phase 2: Important parallel data
    console.log('📋 Phase 2: Loading important data in parallel')
    const parallelResults = await this.loadDataItemsParallel(options, strategy.parallel)
    results.push(...parallelResults)

    // Phase 3: Background data (don't wait for completion)
    console.log('📋 Phase 3: Starting background data loading')
    this.loadDataItemsBackground(options, strategy.background)

    return results
  }

  /**
   * Load data items sequentially (for critical data)
   */
  private async loadDataItems(options: PreloadOptions, items: string[]): Promise<PreloadResult[]> {
    const results: PreloadResult[] = []
    
    for (const item of items) {
      const result = await this.loadSingleDataItem(options, item)
      results.push(result)
      
      // Short delay between critical items to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 50))
    }
    
    return results
  }

  /**
   * Load data items in parallel (for important but non-critical data)
   */
  private async loadDataItemsParallel(options: PreloadOptions, items: string[]): Promise<PreloadResult[]> {
    const promises = items.map(item => this.loadSingleDataItem(options, item))
    return await Promise.allSettled(promises).then(results => 
      results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          success: false,
          module: 'unknown',
          dataType: 'unknown',
          cached: false,
          loadTime: 0,
          error: 'Promise rejected'
        }
      )
    )
  }

  /**
   * Load data items in background (fire and forget)
   */
  private loadDataItemsBackground(options: PreloadOptions, items: string[]): void {
    items.forEach(item => {
      this.loadSingleDataItem(options, item).catch(error => {
        console.warn(`Background preload failed for ${item}:`, error)
      })
    })
  }

  /**
   * Load a single data item
   */
  private async loadSingleDataItem(options: PreloadOptions, item: string): Promise<PreloadResult> {
    const startTime = Date.now()
    const [module, dataType] = item.split(':')
    const { userId, timeRanges = {} } = options

    try {
      // Check if data is already cached using the correct method for each module
      const cacheKey = this.generateCacheKey(module, dataType)
      let cachedData: any = null

      if (module === 'gmail') {
        // Use the specific email cache method that matches API routes
        const category = dataType // inbox, sent, drafts
        cachedData = unifiedCacheService.getCachedEmailList(userId, category, 'all')
      } else {
        cachedData = unifiedCacheService.getCachedData(module as any, cacheKey, userId)
      }

      if (cachedData?.success && cachedData.data) {
        return {
          success: true,
          module,
          dataType,
          cached: true,
          loadTime: Date.now() - startTime
        }
      }

      // Load fresh data based on module and type
      const freshData = await this.fetchFreshData(module, dataType, options)

      // Cache the fresh data using the correct method for each module
      if (freshData) {
        if (module === 'gmail') {
          // Use the specific email cache method that matches API routes
          const category = dataType // inbox, sent, drafts
          unifiedCacheService.cacheEmailList(userId, freshData, category, 'all')
        } else {
          unifiedCacheService.cacheData(module as any, cacheKey, freshData, userId)
        }
      }

      return {
        success: true,
        module,
        dataType,
        cached: false,
        loadTime: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        module,
        dataType,
        cached: false,
        loadTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get email date range that matches EmailPageLayout default (past 7 days)
   */
  private getEmailDateRange(): { from: Date; to: Date } {
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(today.getDate() - 7)
    sevenDaysAgo.setHours(0, 0, 0, 0) // Start of the day 7 days ago

    return { from: sevenDaysAgo, to: today }
  }

  /**
   * Generate cache key for data item - matches API route cache keys
   */
  private generateCacheKey(module: string, dataType: string): string {
    switch (`${module}:${dataType}`) {
      case 'gmail:inbox':
        const inboxDateRange = this.getEmailDateRange()
        return `emails:list:inbox:${inboxDateRange.from.toISOString()}:${inboxDateRange.to.toISOString()}`
      case 'gmail:sent':
        const sentDateRange = this.getEmailDateRange()
        return `emails:list:sent:${sentDateRange.from.toISOString()}:${sentDateRange.to.toISOString()}`
      case 'gmail:drafts':
        const draftsDateRange = this.getEmailDateRange()
        return `emails:list:drafts:${draftsDateRange.from.toISOString()}:${draftsDateRange.to.toISOString()}`
      case 'gmail:labels':
        return 'labels:all'
      case 'calendar:today':
        const currentDate = new Date()
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
        return `events:${startOfMonth.toISOString()}:${endOfMonth.toISOString()}`
      case 'calendar:thisWeek':
        const weekCurrentDate = new Date()
        const weekStartOfMonth = new Date(weekCurrentDate.getFullYear(), weekCurrentDate.getMonth(), 1)
        const weekEndOfMonth = new Date(weekCurrentDate.getFullYear(), weekCurrentDate.getMonth() + 1, 0)
        return `events:${weekStartOfMonth.toISOString()}:${weekEndOfMonth.toISOString()}`
      case 'calendar:thisMonth':
        const monthCurrentDate = new Date()
        const monthStart = new Date(monthCurrentDate.getFullYear(), monthCurrentDate.getMonth(), 1)
        const monthEnd = new Date(monthCurrentDate.getFullYear(), monthCurrentDate.getMonth() + 1, 0)
        return `events:${monthStart.toISOString()}:${monthEnd.toISOString()}`
      case 'calendar:colors':
        return 'colors:all'
      default:
        return `${dataType}:default`
    }
  }

  /**
   * Fetch fresh data from APIs - returns data structure that matches API route caching
   */
  private async fetchFreshData(module: string, dataType: string, options: PreloadOptions): Promise<any> {
    const { timeRanges = {} } = options

    console.log(`🔄 [PRELOADER] Fetching fresh data for ${module}:${dataType}`)

    try {
      switch (`${module}:${dataType}`) {
        case 'gmail:inbox':
          // Use same 7-day date range as EmailPageLayout
          const inboxDateRange = this.getEmailDateRange()
          const inboxParams = new URLSearchParams({
            limit: (timeRanges.gmail?.maxEmails || 50).toString(),
            dateFrom: inboxDateRange.from.toISOString(),
            dateTo: inboxDateRange.to.toISOString()
          })
          const inboxResponse = await fetch(`/api/gmail/inbox?${inboxParams}`)
          if (inboxResponse.ok) {
            const data = await inboxResponse.json()
            // Return the full cached email list structure that matches API routes
            return {
              emails: data.emails || [],
              totalCount: data.totalCount || 0,
              nextPageToken: data.nextPageToken,
              timestamp: Date.now(),
              category: 'inbox',
              query: 'all'
            }
          }
          return null

        case 'gmail:sent':
          // Use same 7-day date range as EmailPageLayout
          const sentDateRange = this.getEmailDateRange()
          const sentParams = new URLSearchParams({
            limit: (timeRanges.gmail?.maxEmails || 25).toString(),
            dateFrom: sentDateRange.from.toISOString(),
            dateTo: sentDateRange.to.toISOString()
          })
          const sentResponse = await fetch(`/api/gmail/sent?${sentParams}`)
          if (sentResponse.ok) {
            const data = await sentResponse.json()
            return {
              emails: data.emails || [],
              totalCount: data.totalCount || 0,
              nextPageToken: data.nextPageToken,
              timestamp: Date.now(),
              category: 'sent',
              query: 'all'
            }
          }
          return null

        case 'gmail:drafts':
          // Use same 7-day date range as EmailPageLayout
          const draftsDateRange = this.getEmailDateRange()
          const draftsParams = new URLSearchParams({
            limit: (timeRanges.gmail?.maxEmails || 25).toString(),
            dateFrom: draftsDateRange.from.toISOString(),
            dateTo: draftsDateRange.to.toISOString()
          })
          const draftsResponse = await fetch(`/api/gmail/drafts?${draftsParams}`)
          if (draftsResponse.ok) {
            const data = await draftsResponse.json()
            return {
              emails: data.drafts || [],
              totalCount: data.totalCount || 0,
              nextPageToken: data.nextPageToken,
              timestamp: Date.now(),
              category: 'drafts',
              query: 'all'
            }
          }
          return null

        case 'gmail:labels':
          const labelsResponse = await fetch('/api/gmail/labels')
          return labelsResponse.ok ? (await labelsResponse.json()).labels : null

        case 'calendar:today':
          // Use current month range like calendar page does
          const currentDate = new Date()
          const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
          const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
          const todayParams = new URLSearchParams({
            timeMin: startOfMonth.toISOString(),
            timeMax: endOfMonth.toISOString(),
            maxResults: '50'
          })
          const todayResponse = await fetch(`/api/calendar?${todayParams}`)
          return todayResponse.ok ? (await todayResponse.json()).events : null

        case 'calendar:thisWeek':
          // Use current month range like calendar page does
          const weekCurrentDate = new Date()
          const weekStartOfMonth = new Date(weekCurrentDate.getFullYear(), weekCurrentDate.getMonth(), 1)
          const weekEndOfMonth = new Date(weekCurrentDate.getFullYear(), weekCurrentDate.getMonth() + 1, 0)
          const weekParams = new URLSearchParams({
            timeMin: weekStartOfMonth.toISOString(),
            timeMax: weekEndOfMonth.toISOString(),
            maxResults: '50'
          })
          const weekResponse = await fetch(`/api/calendar?${weekParams}`)
          return weekResponse.ok ? (await weekResponse.json()).events : null

        case 'calendar:thisMonth':
          // Use current month range like calendar page does
          const monthCurrentDate = new Date()
          const monthStart = new Date(monthCurrentDate.getFullYear(), monthCurrentDate.getMonth(), 1)
          const monthEnd = new Date(monthCurrentDate.getFullYear(), monthCurrentDate.getMonth() + 1, 0)
          const monthParams = new URLSearchParams({
            timeMin: monthStart.toISOString(),
            timeMax: monthEnd.toISOString(),
            maxResults: '50'
          })
          const monthResponse = await fetch(`/api/calendar?${monthParams}`)
          return monthResponse.ok ? (await monthResponse.json()).events : null

        case 'calendar:colors':
          const colorsResponse = await fetch('/api/calendar/colors')
          return colorsResponse.ok ? (await colorsResponse.json()).colors : null





        default:
          console.warn(`Unknown data type for preloading: ${module}:${dataType}`)
          return null
      }
    } catch (error) {
      console.error(`Error fetching ${module}:${dataType}:`, error)
      return null
    }
  }

  /**
   * Check if preloading is currently in progress
   */
  get isCurrentlyPreloading(): boolean {
    return this.isPreloading
  }

  /**
   * Get preloading progress for a user
   */
  getPreloadingProgress(userId: string): { inProgress: boolean; queueSize: number } {
    return {
      inProgress: this.preloadQueue.has(`preload:${userId}`),
      queueSize: this.preloadQueue.size
    }
  }
}

// Export singleton instance
export const appPreloader = new AppPreloader()

// Export convenience functions
export const preloadAllAppData = (options: PreloadOptions) => appPreloader.preloadAllAppData(options)
export const isPreloading = () => appPreloader.isCurrentlyPreloading
export const getPreloadingProgress = (userId: string) => appPreloader.getPreloadingProgress(userId)
