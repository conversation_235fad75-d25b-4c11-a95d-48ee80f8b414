import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentTaskService } from '@/lib/chat/aiAgentTasks'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

interface RouteParams {
  params: {
    taskId: string
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-task-update', { endpoint: `/api/ai-agent/tasks/${params.taskId}` })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-task-update')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { taskId } = params
    const {
      title,
      description,
      status,
      priority,
      dueDate,
      reminderDate,
      userFeedback
    } = await request.json()

    const result = await aiAgentTaskService.updateTask(session.user.id, taskId, {
      title,
      description,
      status,
      priority,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      reminderDate: reminderDate ? new Date(reminderDate) : undefined,
      userFeedback
    })

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-task-update')
    if (duration && duration > 2000) {
      console.warn(`Slow AI task update: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-task-update')
    console.error('Update task error:', error)
    return NextResponse.json(
      { error: 'Failed to update task' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-task-delete', { endpoint: `/api/ai-agent/tasks/${params.taskId}` })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-task-delete')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { taskId } = params

    const result = await aiAgentTaskService.deleteTask(session.user.id, taskId)

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-task-delete')
    if (duration && duration > 1500) {
      console.warn(`Slow AI task deletion: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-task-delete')
    console.error('Delete task error:', error)
    return NextResponse.json(
      { error: 'Failed to delete task' },
      { status: 500 }
    )
  }
}
