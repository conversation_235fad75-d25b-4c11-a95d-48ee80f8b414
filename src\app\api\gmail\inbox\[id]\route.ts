import { NextRequest } from "next/server"
import {
  getEmailDetails,
  markEmailAsRead,
  markEmailAsUnread,
  deleteEmail,
  archiveEmail,
  starEmail,
  addEmailLabel,
  removeEmailLabel,
  withGmailAuth,
  formatEmailOperationResult
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { emailActionSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return with<PERSON><PERSON>Auth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-email-get', { userId: user.id })

    const { id: messageId } = await params

    // Fetch email details from Gmail
    const email = await getEmailDetails(user.id, messageId)

    if (!email) {
      endPerformanceMetric('gmail-email-get')
      throw new Error("Email not found")
    }

    // End performance monitoring
    endPerformanceMetric('gmail-email-get')

    return { email }
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-email-action', { userId: user.id })

    const { id: messageId } = await params

    // Validate request body using Zod
    const validation = await validateRequestBody(request, emailActionSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-email-action')
      throw new Error(`Invalid email action data: ${validation.error}`)
    }

    const { action, value, labelId } = validation.data

    let success = false
    let message = ""

    switch (action) {
      case 'read':
        success = await markEmailAsRead(user.id, messageId)
        message = "Email marked as read"
        break

      case 'unread':
        success = await markEmailAsUnread(user.id, messageId)
        message = "Email marked as unread"
        break

      case 'trash':
        success = await deleteEmail(user.id, messageId)
        message = "Email deleted"
        break
      
      case 'archive':
        success = await archiveEmail(user.id, messageId)
        message = "Email archived"
        break
      
      case 'star':
        success = await starEmail(user.id, messageId, value === true)
        message = value === true ? "Email starred" : "Email unstarred"
        break
      
      case 'addLabel':
        if (!labelId) {
          throw new Error("Label ID is required for addLabel action")
        }
        success = await addEmailLabel(user.id, messageId, labelId)
        message = "Label added to email"
        break
      
      case 'removeLabel':
        if (!labelId) {
          throw new Error("Label ID is required for removeLabel action")
        }
        success = await removeEmailLabel(user.id, messageId, labelId)
        message = "Label removed from email"
        break
      
      default:
        throw new Error(`Invalid action: ${action}`)
    }

    // End performance monitoring
    endPerformanceMetric('gmail-email-action')

    return formatEmailOperationResult(success, message)
  })
}