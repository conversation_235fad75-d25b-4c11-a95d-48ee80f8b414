/**
 * Calendar Cache Helper - Unified Cache System
 * Calendar-specific caching functions using the unified cache manager
 */

import { cacheManager } from './cacheManager'
import {
  CachedCalendarEvent,
  CachedCalendarList,
  CacheOperationResult
} from './types'

const MODULE_NAME = 'calendar'

/**
 * Calendar Event List Caching
 */
export function getCachedCalendarEvents(
  userId: string,
  timeMin?: string,
  timeMax?: string,
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarList> {
  const key = `events:list:${calendarId}:${timeMin || 'all'}:${timeMax || 'all'}`
  return cacheManager.get<CachedCalendarList>(MODULE_NAME, key, userId)
}

export function setCachedCalendarEvents(
  userId: string,
  events: CachedCalendarList,
  timeMin?: string,
  timeMax?: string,
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarList> {
  const key = `events:list:${calendarId}:${timeMin || 'all'}:${timeMax || 'all'}`
  return cacheManager.set(MODULE_NAME, key, events, userId)
}

export function isCalendarCacheValid(
  userId: string,
  timeMin?: string,
  timeMax?: string,
  calendarId: string = 'primary'
): boolean {
  const result = getCachedCalendarEvents(userId, timeMin, timeMax, calendarId)
  return result.success && result.fromCache
}

/**
 * Calendar Event Detail Caching
 */
export function getCachedCalendarEvent(
  userId: string,
  eventId: string,
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarEvent> {
  const key = `event:detail:${calendarId}:${eventId}`
  return cacheManager.get<CachedCalendarEvent>(MODULE_NAME, key, userId)
}

export function setCachedCalendarEvent(
  userId: string,
  event: CachedCalendarEvent,
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarEvent> {
  const key = `event:detail:${calendarId}:${event.id}`
  return cacheManager.set(MODULE_NAME, key, event, userId)
}

export function updateCachedCalendarEvent(
  userId: string,
  eventId: string,
  updates: Partial<CachedCalendarEvent>,
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarEvent> {
  const existing = getCachedCalendarEvent(userId, eventId, calendarId)
  if (existing.success && existing.data) {
    const updatedEvent = { ...existing.data, ...updates }
    return setCachedCalendarEvent(userId, updatedEvent, calendarId)
  }
  return {
    success: false,
    error: 'Calendar event not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * Calendar Availability Caching
 */
export function getCachedAvailability(
  userId: string,
  timeMin: string,
  timeMax: string
): CacheOperationResult<any> {
  const key = `availability:${timeMin}:${timeMax}`
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedAvailability(
  userId: string,
  availability: any,
  timeMin: string,
  timeMax: string
): CacheOperationResult<any> {
  const key = `availability:${timeMin}:${timeMax}`
  return cacheManager.set(MODULE_NAME, key, availability, userId)
}

/**
 * Calendar List Caching
 */
export function getCachedCalendarList(
  userId: string
): CacheOperationResult<any[]> {
  const key = 'calendars:list'
  return cacheManager.get<any[]>(MODULE_NAME, key, userId)
}

export function setCachedCalendarList(
  userId: string,
  calendars: any[]
): CacheOperationResult<any[]> {
  const key = 'calendars:list'
  return cacheManager.set(MODULE_NAME, key, calendars, userId)
}

/**
 * Cache Invalidation
 */
export function invalidateCalendarCache(
  userId: string,
  calendarId?: string,
  timeRange?: { timeMin?: string; timeMax?: string }
): number {
  let pattern = 'events:list'
  if (calendarId) {
    pattern += `:${calendarId}`
  }
  if (timeRange) {
    pattern += `:${timeRange.timeMin || 'all'}:${timeRange.timeMax || 'all'}`
  }
  
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

export function invalidateCalendarEvent(
  userId: string,
  eventId: string,
  calendarId: string = 'primary'
): boolean {
  return cacheManager.delete(MODULE_NAME, `event:detail:${calendarId}:${eventId}`, userId)
}

export function invalidateAvailability(
  userId: string,
  timeMin?: string,
  timeMax?: string
): number {
  const pattern = timeMin && timeMax ? `availability:${timeMin}:${timeMax}` : 'availability'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

/**
 * Bulk Operations
 */
export function cacheMultipleCalendarEvents(
  userId: string,
  events: CachedCalendarEvent[],
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarEvent[]> {
  const results: CacheOperationResult<CachedCalendarEvent>[] = []
  
  events.forEach(event => {
    const result = setCachedCalendarEvent(userId, event, calendarId)
    results.push(result)
  })

  const allSuccessful = results.every(r => r.success)
  
  return {
    success: allSuccessful,
    data: allSuccessful ? events : undefined,
    error: allSuccessful ? undefined : 'Some events failed to cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

export function getMultipleCachedCalendarEvents(
  userId: string,
  eventIds: string[],
  calendarId: string = 'primary'
): CacheOperationResult<CachedCalendarEvent[]> {
  const events: CachedCalendarEvent[] = []
  const notFound: string[] = []

  eventIds.forEach(eventId => {
    const result = getCachedCalendarEvent(userId, eventId, calendarId)
    if (result.success && result.data) {
      events.push(result.data)
    } else {
      notFound.push(eventId)
    }
  })

  return {
    success: notFound.length === 0,
    data: events,
    error: notFound.length > 0 ? `Events not found: ${notFound.join(', ')}` : undefined,
    fromCache: true,
    timestamp: Date.now()
  }
}

/**
 * Preloading
 */
export async function preloadCalendarData(
  userId: string,
  options: {
    loadUpcoming?: boolean
    loadToday?: boolean
    loadWeek?: boolean
    loadMonth?: boolean
    calendarIds?: string[]
  } = {}
): Promise<CacheOperationResult<any>> {
  try {
    const preloadTasks: Promise<any>[] = []

    if (options.loadToday !== false) {
      console.log(`Preloading today's events for user ${userId}`)
    }

    if (options.loadUpcoming) {
      console.log(`Preloading upcoming events for user ${userId}`)
    }

    if (options.loadWeek) {
      console.log(`Preloading week events for user ${userId}`)
    }

    if (options.loadMonth) {
      console.log(`Preloading month events for user ${userId}`)
    }

    await Promise.all(preloadTasks)

    return {
      success: true,
      data: { preloaded: true },
      fromCache: false,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Preload failed',
      fromCache: false,
      timestamp: Date.now()
    }
  }
}

/**
 * Statistics and Cleanup
 */
export function getCalendarCacheStats() {
  const globalStats = cacheManager.getStats()
  return {
    ...globalStats.moduleStats.calendar,
    hitRate: globalStats.hitRate,
    missRate: globalStats.missRate,
    totalEntries: globalStats.totalEntries,
    memoryUsage: globalStats.memoryUsage
  }
}

export function cleanupCalendarCache(userId?: string): number {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    force: false // Only cleanup expired entries
  })
}

/**
 * Constants
 */
export const CALENDAR_CACHE_KEYS = {
  EVENT_LIST: 'events:list',
  EVENT_DETAIL: 'event:detail',
  CALENDARS: 'calendars:list',
  AVAILABILITY: 'availability'
} as const

export const CALENDAR_CACHE_TTL = 15 * 60 * 1000 // 15 minutes

/**
 * Legacy compatibility functions
 */
export function getCachedCalendarData(userId: string, key: string) {
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedCalendarData(userId: string, key: string, data: any) {
  return cacheManager.set(MODULE_NAME, key, data, userId)
}

export function invalidateCalendarData(userId: string, pattern?: string) {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}
