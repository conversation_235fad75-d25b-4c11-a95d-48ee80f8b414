import { NextRequest, NextResponse } from 'next/server'
import { createApiErrorResponse } from '@/lib/validation/utils'

/**
 * Generic query handler interface
 */
export interface QueryHandler<T = any> {
  (userId: string, searchParams: URLSearchParams, ...args: any[]): Promise<NextResponse>
}

/**
 * Query handler configuration
 */
export interface QueryHandlerConfig<T = any> {
  defaultType?: string
  handlers: Record<string, QueryHandler<T>>
  authenticationRequired?: boolean
  validateParams?: (searchParams: URLSearchParams) => boolean
}

/**
 * Generic query type router that eliminates duplicate switch statements
 */
export async function handleQueryTypes<T = any>(
  request: NextRequest,
  config: QueryHandlerConfig<T>,
  authenticateUser?: (request: NextRequest) => Promise<{
    success: boolean
    response?: NextResponse
    data?: { user: any; url: URL }
  }>,
  ...additionalArgs: any[]
): Promise<NextResponse> {
  try {
    let user: any
    let url: URL

    // Handle authentication if required
    if (config.authenticationRequired && authenticateUser) {
      const authResult = await authenticateUser(request)
      if (!authResult.success) {
        return authResult.response!
      }
      user = authResult.data!.user
      url = authResult.data!.url
    } else {
      url = new URL(request.url)
    }

    const searchParams = url.searchParams

    // Validate parameters if validator provided
    if (config.validateParams && !config.validateParams(searchParams)) {
      return NextResponse.json(
        { error: 'Invalid query parameters' },
        { status: 400 }
      )
    }

    // Get query type
    const queryType = searchParams.get('type') || config.defaultType || 'list'

    // Find handler
    const handler = config.handlers[queryType]
    if (!handler) {
      return NextResponse.json(
        { error: `Unsupported query type: ${queryType}` },
        { status: 400 }
      )
    }

    // Execute handler
    const userId = user?.id
    return await handler(userId, searchParams, ...additionalArgs)

  } catch (error) {
    const { response } = createApiErrorResponse(error, {
      operation: 'Query Handler',
      logError: true
    })
    return new NextResponse(response.body, {
      status: response.status,
      headers: response.headers
    })
  }
}

/**
 * Common pagination parameter validation
 */
export function validatePaginationParams(searchParams: URLSearchParams): boolean {
  const pageSize = searchParams.get('pageSize')
  const pageToken = searchParams.get('pageToken')

  if (pageSize) {
    const size = parseInt(pageSize, 10)
    if (isNaN(size) || size < 1 || size > 100) {
      return false
    }
  }

  // pageToken validation would depend on the specific API
  // For now, just check it's a string if provided
  if (pageToken && typeof pageToken !== 'string') {
    return false
  }

  return true
}

/**
 * Common date range parameter validation
 */
export function validateDateRangeParams(searchParams: URLSearchParams): boolean {
  const startTime = searchParams.get('startTime')
  const endTime = searchParams.get('endTime')

  if (startTime && !isValidISODate(startTime)) {
    return false
  }

  if (endTime && !isValidISODate(endTime)) {
    return false
  }

  // Ensure start time is before end time if both provided
  if (startTime && endTime) {
    const start = new Date(startTime)
    const end = new Date(endTime)
    if (start >= end) {
      return false
    }
  }

  return true
}

/**
 * Validate ISO date string
 */
function isValidISODate(dateString: string): boolean {
  const date = new Date(dateString)
  return date instanceof Date && !isNaN(date.getTime()) && 
         dateString === date.toISOString()
}

/**
 * Create standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  metadata?: {
    totalCount?: number
    nextPageToken?: string
    hasMore?: boolean
    timestamp?: string
  }
): NextResponse {
  const response: any = {
    success: true,
    data
  }

  if (metadata) {
    response.metadata = {
      timestamp: new Date().toISOString(),
      ...metadata
    }
  }

  return NextResponse.json(response)
}

/**
 * Extract common query parameters
 */
export function extractCommonParams(searchParams: URLSearchParams) {
  return {
    pageSize: parseInt(searchParams.get('pageSize') || '20', 10),
    pageToken: searchParams.get('pageToken') || undefined,
    startTime: searchParams.get('startTime') || undefined,
    endTime: searchParams.get('endTime') || undefined,
    filter: searchParams.get('filter') || undefined,
    orderBy: searchParams.get('orderBy') || undefined,
    query: searchParams.get('q') || searchParams.get('query') || undefined
  }
}

/**
 * Utility for handling conference-related queries (Meet API pattern)
 */
export function createConferenceQueryHandlers() {
  return {
    list: async (userId: string, searchParams: URLSearchParams) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    recent: async (userId: string, searchParams: URLSearchParams) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    statistics: async (userId: string, searchParams: URLSearchParams) => {
      // Implementation would go here
      return createSuccessResponse({})
    },
    search: async (userId: string, searchParams: URLSearchParams) => {
      // Implementation would go here
      return createSuccessResponse([])
    }
  }
}

/**
 * Utility for handling participant-related queries (Meet API pattern)
 */
export function createParticipantQueryHandlers() {
  return {
    list: async (userId: string, searchParams: URLSearchParams, conferenceId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    statistics: async (userId: string, searchParams: URLSearchParams, conferenceId: string) => {
      // Implementation would go here
      return createSuccessResponse({})
    },
    attendance: async (userId: string, searchParams: URLSearchParams, conferenceId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    search: async (userId: string, searchParams: URLSearchParams, conferenceId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    detailed: async (userId: string, searchParams: URLSearchParams, conferenceId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    }
  }
}

/**
 * Utility for handling transcript-related queries (Meet API pattern)
 */
export function createTranscriptQueryHandlers() {
  return {
    details: async (userId: string, searchParams: URLSearchParams, transcriptId: string) => {
      // Implementation would go here
      return createSuccessResponse({})
    },
    fullText: async (userId: string, searchParams: URLSearchParams, transcriptId: string) => {
      // Implementation would go here
      return createSuccessResponse('')
    },
    entries: async (userId: string, searchParams: URLSearchParams, transcriptId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    },
    search: async (userId: string, searchParams: URLSearchParams, transcriptId: string) => {
      // Implementation would go here
      return createSuccessResponse([])
    }
  }
}
