"use client"

import React, { createContext, useContext, useCallback, useState, useEffect } from 'react'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'
import { CacheStats } from '@/lib/cache/unified/types'

interface UnifiedCacheContextType {
  // Cache operations
  getCachedData: <T>(module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', key: string, userId?: string) => T | null
  setCachedData: <T>(module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', key: string, data: T, userId?: string) => boolean
  
  // Cache management
  invalidateCache: (module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', pattern?: string, userId?: string) => number
  clearAllCache: () => void
  
  // Statistics and monitoring
  getStats: () => CacheStats
  getNamespaceSize: (namespace: string) => number
  
  // Preloading
  preloadAllData: (userId?: string) => Promise<void>
  isPreloading: boolean
  
  // Gmail specific
  preloadGmailData: (userId?: string) => Promise<void>
  
  // Calendar specific
  preloadCalendarData: (userId?: string) => Promise<void>
  
  // Meet specific
  preloadMeetData: (userId?: string) => Promise<void>
  
  // AI Agent specific
  preloadAiAgentData: (userId?: string) => Promise<void>
}

const UnifiedCacheContext = createContext<UnifiedCacheContextType | null>(null)

interface UnifiedCacheProviderProps {
  children: React.ReactNode
  userId?: string
  config?: {
    gmail?: { ttl?: number; maxSize?: number }
    calendar?: { ttl?: number; maxSize?: number }
    meet?: { ttl?: number; maxSize?: number }
    aiAgent?: { ttl?: number; maxSize?: number }
  }
  autoPreload?: boolean
}

export function UnifiedCacheProvider({ 
  children, 
  userId, 
  config,
  autoPreload = false 
}: UnifiedCacheProviderProps) {
  const [isPreloading, setIsPreloading] = useState(false)

  // Cache operations
  const getCachedData = useCallback(<T,>(
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent',
    key: string,
    userIdOverride?: string
  ): T | null => {
    const result = unifiedCacheService.getCachedData<T>(module, key, userIdOverride || userId)
    return result.success && result.data !== undefined ? result.data : null
  }, [userId])

  const setCachedData = useCallback(<T,>(
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', 
    key: string, 
    data: T, 
    userIdOverride?: string
  ): boolean => {
    const result = unifiedCacheService.cacheData(module, key, data, userIdOverride || userId)
    return result.success
  }, [userId])

  // Cache management
  const invalidateCache = useCallback((
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent',
    pattern?: string,
    userIdOverride?: string
  ): number => {
    return unifiedCacheService.invalidateCache({
      module,
      pattern,
      userId: userIdOverride || userId
    })
  }, [userId])

  const clearAllCache = useCallback(() => {
    // Clear cache for all modules
    unifiedCacheService.invalidateCache({ module: 'gmail' })
    unifiedCacheService.invalidateCache({ module: 'calendar' })
    unifiedCacheService.invalidateCache({ module: 'meet' })
    unifiedCacheService.invalidateCache({ module: 'aiAgent' })
  }, [])

  // Statistics
  const getStats = useCallback((): CacheStats => {
    return unifiedCacheService.getStats()
  }, [])

  const getNamespaceSize = useCallback((_namespace: string): number => {
    // This would need to be implemented in the unified cache service
    return 0
  }, [])

  // Preloading functions
  const preloadGmailData = useCallback(async (userIdOverride?: string) => {
    const targetUserId = userIdOverride || userId
    if (!targetUserId) return

    try {
      // Preload common Gmail data
      const response = await fetch(`/api/gmail/inbox?maxResults=50`)
      if (response.ok) {
        const data = await response.json()
        unifiedCacheService.cacheData('gmail', 'inbox:emails', data.emails || [], targetUserId)
      }
    } catch (error) {
      console.error('Failed to preload Gmail data:', error)
    }
  }, [userId])

  const preloadCalendarData = useCallback(async (userIdOverride?: string) => {
    const targetUserId = userIdOverride || userId
    if (!targetUserId) return

    try {
      // Preload calendar events for next 30 days
      const timeMin = new Date().toISOString()
      const timeMax = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()

      const response = await fetch(`/api/calendar?timeMin=${timeMin}&timeMax=${timeMax}&maxResults=100`)
      if (response.ok) {
        const data = await response.json()
        unifiedCacheService.cacheData('calendar', `events:${timeMin}:${timeMax}`, data.events || [], targetUserId)
      }
    } catch (error) {
      console.error('Failed to preload Calendar data:', error)
    }
  }, [userId])

  const preloadMeetData = useCallback(async (userIdOverride?: string) => {
    const targetUserId = userIdOverride || userId
    if (!targetUserId) return

    try {
      // Preload Meet conferences
      const response = await fetch('/api/meet/conferences')
      if (response.ok) {
        const data = await response.json()
        unifiedCacheService.cacheData('meet', 'conferences', data.conferences || [], targetUserId)
      }
    } catch (error) {
      console.error('Failed to preload Meet data:', error)
    }
  }, [userId])

  const preloadAiAgentData = useCallback(async (userIdOverride?: string) => {
    const targetUserId = userIdOverride || userId
    if (!targetUserId) return

    try {
      // Preload AI Agent conversations or context
      // This would depend on your AI Agent API structure
      console.log('AI Agent data preloading not yet implemented')
    } catch (error) {
      console.error('Failed to preload AI Agent data:', error)
    }
  }, [userId])

  const preloadAllData = useCallback(async (userIdOverride?: string) => {
    const targetUserId = userIdOverride || userId
    if (!targetUserId) {
      console.warn('No userId provided for cache preloading')
      return
    }

    setIsPreloading(true)
    try {
      console.log('🚀 Starting comprehensive data preloading for user:', targetUserId)
      
      await Promise.allSettled([
        preloadGmailData(targetUserId),
        preloadCalendarData(targetUserId),
        preloadMeetData(targetUserId),
        preloadAiAgentData(targetUserId)
      ])
      
      console.log('✅ Comprehensive data preloading completed')
    } catch (error) {
      console.error('❌ Error during comprehensive data preloading:', error)
    } finally {
      setIsPreloading(false)
    }
  }, [userId, preloadGmailData, preloadCalendarData, preloadMeetData, preloadAiAgentData])

  // Auto preload on mount if enabled
  useEffect(() => {
    if (autoPreload && userId) {
      preloadAllData(userId)
    }
  }, [autoPreload, userId, preloadAllData])

  const contextValue: UnifiedCacheContextType = {
    getCachedData,
    setCachedData,
    invalidateCache,
    clearAllCache,
    getStats,
    getNamespaceSize,
    preloadAllData,
    isPreloading,
    preloadGmailData,
    preloadCalendarData,
    preloadMeetData,
    preloadAiAgentData
  }

  return (
    <UnifiedCacheContext.Provider value={contextValue}>
      {children}
    </UnifiedCacheContext.Provider>
  )
}

export function useUnifiedCache(): UnifiedCacheContextType {
  const context = useContext(UnifiedCacheContext)
  if (!context) {
    throw new Error('useUnifiedCache must be used within a UnifiedCacheProvider')
  }
  return context
}

export function useEmailCache(userId?: string) {
  const cache = useUnifiedCache()
  
  return {
    getEmails: (folder: string) => cache.getCachedData('gmail', `emails:${folder}`, userId),
    setEmails: (folder: string, emails: any[]) => cache.setCachedData('gmail', `emails:${folder}`, emails, userId),
    invalidateEmails: (folder?: string) => cache.invalidateCache('gmail', folder ? `emails:${folder}` : 'emails:', userId),
    preloadEmails: () => cache.preloadGmailData(userId)
  }
}
