import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unifiedGoogleClient'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'
import { generateFreeBusyCacheKey } from '@/lib/cache/utils/calendarConversion'

// POST /api/calendar/freebusy - Query free/busy information
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const requestData = await request.json()

    // Validate required fields
    if (!requestData.timeMin || !requestData.timeMax) {
      return NextResponse.json({ 
        error: 'timeMin and timeMax are required' 
      }, { status: 400 })
    }

    if (!requestData.items || !Array.isArray(requestData.items)) {
      return NextResponse.json({ 
        error: 'items array is required' 
      }, { status: 400 })
    }

    // Create cache key based on parameters
    const calendarIds = requestData.items.map((item: any) => item.id || item.calendarId)
    const cacheKey = generateFreeBusyCacheKey(
      requestData.timeMin,
      requestData.timeMax,
      calendarIds
    )

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedFreeBusy(
      session.user.id,
      requestData.timeMin,
      requestData.timeMax
    )

    if (cachedResult.success && cachedResult.data) {
      console.log('Calendar FreeBusy API - Returning cached data')
      return NextResponse.json({
        freebusy: cachedResult.data,
        message: 'Free/busy information retrieved successfully',
        cached: true
      })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      const freeBusyRequest = {
        timeMin: requestData.timeMin,
        timeMax: requestData.timeMax,
        timeZone: requestData.timeZone || 'UTC',
        groupExpansionMax: requestData.groupExpansionMax || 25,
        calendarExpansionMax: requestData.calendarExpansionMax || 50,
        items: requestData.items.map((item: any) => ({
          id: item.id || item.calendarId
        }))
      }

      const response = await calendar.freebusy.query({
        requestBody: freeBusyRequest
      })

      // Cache the free/busy data
      if (response.data) {
        unifiedCacheService.cacheFreeBusy(
          session.user.id,
          response.data,
          requestData.timeMin,
          requestData.timeMax
        )
        console.log(`📅 Cached free/busy data for user: ${session.user.id}`)
      }

      return NextResponse.json({
        freebusy: response.data,
        message: 'Free/busy information retrieved successfully'
      })

    } catch (error) {
      console.error('Google Calendar freebusy error:', error)
      return NextResponse.json(
        { error: 'Failed to query free/busy information. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar freebusy error:', error)
    return NextResponse.json(
      { error: 'Failed to query free/busy information' },
      { status: 500 }
    )
  }
} 