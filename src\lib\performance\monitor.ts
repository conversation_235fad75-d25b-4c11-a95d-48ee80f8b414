interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

interface ApiCallMetric {
  endpoint: string
  method: string
  startTime: number
  endTime?: number
  duration?: number
  status?: number
  cached?: boolean
  error?: string
  userId?: string
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map()
  private apiCalls: ApiCallMetric[] = []
  private cacheHits: number = 0
  private cacheMisses: number = 0
  private totalRequests: number = 0

  startMetric(name: string, metadata?: Record<string, any>): void {
    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata
    })
  }

  endMetric(name: string): number | null {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    if (duration > 1000) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`, metric.metadata)
    }

    return duration
  }

  trackApiCall(
    endpoint: string,
    method: string,
    options: {
      userId?: string
      cached?: boolean
      status?: number
      error?: string
    } = {}
  ): string {
    const callId = `${method}_${endpoint}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const apiCall: ApiCallMetric = {
      endpoint,
      method: method.toUpperCase(),
      startTime: performance.now(),
      ...options
    }

    this.apiCalls.push(apiCall)
    this.totalRequests++

    if (options.cached) {
      this.cacheHits++
    } else {
      this.cacheMisses++
    }

    return callId
  }
  
  endApiCall(callId: string, status?: number, error?: string): void {
    // Find the API call by reconstructing the pattern
    const apiCall = this.apiCalls.find(call => 
      !call.endTime && callId.includes(`${call.method}_${call.endpoint}`)
    )

    if (apiCall) {
      apiCall.endTime = performance.now()
      apiCall.duration = apiCall.endTime - apiCall.startTime
      if (status) apiCall.status = status
      if (error) apiCall.error = error

      // Log slow API calls (> 2 seconds)
      if (apiCall.duration > 2000) {
        console.warn(`Slow API call: ${apiCall.method} ${apiCall.endpoint} took ${apiCall.duration.toFixed(2)}ms`)
      }
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    totalRequests: number
    cacheHitRate: number
    averageResponseTime: number
    slowApiCalls: ApiCallMetric[]
    recentMetrics: PerformanceMetric[]
  } {
    const completedApiCalls = this.apiCalls.filter(call => call.duration !== undefined)
    const averageResponseTime = completedApiCalls.length > 0
      ? completedApiCalls.reduce((sum, call) => sum + (call.duration || 0), 0) / completedApiCalls.length
      : 0

    const slowApiCalls = completedApiCalls.filter(call => (call.duration || 0) > 1000)
    const recentMetrics = Array.from(this.metrics.values())
      .filter(metric => metric.duration !== undefined)
      .sort((a, b) => (b.startTime || 0) - (a.startTime || 0))
      .slice(0, 10)

    return {
      totalRequests: this.totalRequests,
      cacheHitRate: this.totalRequests > 0 ? (this.cacheHits / this.totalRequests) * 100 : 0,
      averageResponseTime,
      slowApiCalls,
      recentMetrics
    }
  }

  /**
   * Get API call statistics by endpoint
   */
  getApiStats(): Record<string, {
    count: number
    averageTime: number
    errorRate: number
    cacheHitRate: number
  }> {
    const stats: Record<string, {
      calls: ApiCallMetric[]
      count: number
      averageTime: number
      errorRate: number
      cacheHitRate: number
    }> = {}

    this.apiCalls.forEach(call => {
      const key = `${call.method} ${call.endpoint}`
      if (!stats[key]) {
        stats[key] = {
          calls: [],
          count: 0,
          averageTime: 0,
          errorRate: 0,
          cacheHitRate: 0
        }
      }
      stats[key].calls.push(call)
      stats[key].count++
    })

    // Calculate statistics for each endpoint
    Object.keys(stats).forEach(key => {
      const { calls } = stats[key]
      const completedCalls = calls.filter(call => call.duration !== undefined)
      const errorCalls = calls.filter(call => call.error || (call.status && call.status >= 400))
      const cachedCalls = calls.filter(call => call.cached)

      stats[key].averageTime = completedCalls.length > 0
        ? completedCalls.reduce((sum, call) => sum + (call.duration || 0), 0) / completedCalls.length
        : 0

      stats[key].errorRate = calls.length > 0 ? (errorCalls.length / calls.length) * 100 : 0
      stats[key].cacheHitRate = calls.length > 0 ? (cachedCalls.length / calls.length) * 100 : 0
    })

    // Remove the calls array from the return value
    const result: Record<string, {
      count: number
      averageTime: number
      errorRate: number
      cacheHitRate: number
    }> = {}

    Object.keys(stats).forEach(key => {
      result[key] = {
        count: stats[key].count,
        averageTime: stats[key].averageTime,
        errorRate: stats[key].errorRate,
        cacheHitRate: stats[key].cacheHitRate
      }
    })

    return result
  }

  /**
   * Clear old metrics (keep only last 1000 entries)
   */
  cleanup(): void {
    // Keep only recent API calls
    if (this.apiCalls.length > 1000) {
      this.apiCalls = this.apiCalls.slice(-1000)
    }

    // Clear old metrics
    const now = performance.now()
    const oneHourAgo = now - (60 * 60 * 1000) // 1 hour in milliseconds

    for (const [name, metric] of this.metrics.entries()) {
      if (metric.startTime < oneHourAgo) {
        this.metrics.delete(name)
      }
    }
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics.clear()
    this.apiCalls = []
    this.cacheHits = 0
    this.cacheMisses = 0
    this.totalRequests = 0
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Utility functions for easy use
export function startPerformanceMetric(name: string, metadata?: Record<string, any>): void {
  performanceMonitor.startMetric(name, metadata)
}

export function endPerformanceMetric(name: string): number | null {
  return performanceMonitor.endMetric(name)
}

export function trackApiCall(
  endpoint: string,
  method: string,
  options?: {
    userId?: string
    cached?: boolean
    status?: number
    error?: string
  }
): string {
  return performanceMonitor.trackApiCall(endpoint, method, options)
}

export function endApiCall(callId: string, status?: number, error?: string): void {
  performanceMonitor.endApiCall(callId, status, error)
}

export function getPerformanceStats() {
  return performanceMonitor.getStats()
}

export function getApiStats() {
  return performanceMonitor.getApiStats()
}

// Automatic cleanup every 30 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    performanceMonitor.cleanup()
  }, 30 * 60 * 1000)
}
