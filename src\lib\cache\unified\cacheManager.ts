/**
 * Unified Cache Manager
 * Centralized cache management system for all application modules
 */

import {
  BaseCacheEntry,
  CacheConfig,
  ModuleCacheConfig,
  CacheStats,
  CacheOperationResult,
  InvalidationOptions,
  DEFAULT_CACHE_CONFIG,
  CACHE_KEY_PATTERNS,
  cachedEmailSchema,
  cachedEmailListSchema,
  cachedThreadSchema,
  cachedCalendarEventSchema,
  cachedCalendarListSchema,
  cachedAiConversationSchema,
  cachedUserBehaviorSchema
} from './types'

class UnifiedCacheManager {
  private cache: Map<string, BaseCacheEntry> = new Map()
  private config: ModuleCacheConfig
  private stats: {
    hits: number
    misses: number
    lastCleanup: number
    moduleStats: {
      [module: string]: {
        entries: number
        hits: number
        misses: number
      }
    }
  } = {
    hits: 0,
    misses: 0,
    lastCleanup: Date.now(),
    moduleStats: {}
  }
  private cleanupIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor(config: Partial<ModuleCacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config }
    this.initializeModuleStats()
    this.initializeCleanupIntervals()
  }

  private initializeModuleStats(): void {
    const modules = ['gmail', 'calendar', 'meet', 'aiAgent']
    modules.forEach(module => {
      this.stats.moduleStats[module] = {
        entries: 0,
        hits: 0,
        misses: 0
      }
    })
  }

  /**
   * Initialize cleanup intervals for each module
   */
  private initializeCleanupIntervals(): void {
    Object.entries(this.config).forEach(([module, moduleConfig]) => {
      const interval = setInterval(() => {
        this.cleanupModule(module as keyof ModuleCacheConfig)
      }, moduleConfig.cleanupInterval)
      
      this.cleanupIntervals.set(module, interval)
    })
  }

  /**
   * Generate cache key with module prefix and user context
   */
  private generateKey(module: string, key: string, userId?: string): string {
    const baseKey = userId ? `${module}:${userId}:${key}` : `${module}:${key}`
    return baseKey
  }

  /**
   * Check if cache entry is valid
   */
  private isValid(entry: BaseCacheEntry): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }

  /**
   * Get data from cache
   */
  get<T = any>(module: string, key: string, userId?: string): CacheOperationResult<T> {
    const cacheKey = this.generateKey(module, key, userId)
    const entry = this.cache.get(cacheKey)

    // Update module stats
    const moduleStats = this.stats.moduleStats[module]

    if (!entry) {
      this.stats.misses++
      if (moduleStats) moduleStats.misses++
      return {
        success: false,
        fromCache: false,
        timestamp: Date.now()
      }
    }

    if (!this.isValid(entry)) {
      this.cache.delete(cacheKey)
      this.stats.misses++
      if (moduleStats) {
        moduleStats.misses++
        moduleStats.entries = Math.max(0, moduleStats.entries - 1)
      }
      return {
        success: false,
        fromCache: false,
        timestamp: Date.now()
      }
    }

    this.stats.hits++
    if (moduleStats) moduleStats.hits++
    return {
      success: true,
      data: entry.data,
      fromCache: true,
      timestamp: entry.timestamp
    }
  }

  /**
   * Set data in cache
   */
  set<T = any>(
    module: string,
    key: string,
    data: T,
    userId?: string,
    customTtl?: number
  ): CacheOperationResult<T> {
    const moduleConfig = this.config[module as keyof ModuleCacheConfig]
    if (!moduleConfig) {
      return {
        success: false,
        error: `Unknown module: ${module}`,
        fromCache: false,
        timestamp: Date.now()
      }
    }

    const cacheKey = this.generateKey(module, key, userId)
    const ttl = customTtl || moduleConfig.ttl

    const entry: BaseCacheEntry = {
      data,
      timestamp: Date.now(),
      ttl,
      key: cacheKey,
      userId
    }

    // Check if we need to cleanup before adding
    if (this.cache.size >= moduleConfig.maxSize) {
      this.cleanupModule(module as keyof ModuleCacheConfig)
    }

    // Check if this is a new entry
    const isNewEntry = !this.cache.has(cacheKey)
    this.cache.set(cacheKey, entry)

    // Update module stats
    const moduleStats = this.stats.moduleStats[module]
    if (moduleStats && isNewEntry) {
      moduleStats.entries++
    }

    return {
      success: true,
      data,
      fromCache: false,
      timestamp: entry.timestamp
    }
  }

  /**
   * Delete specific cache entry
   */
  delete(module: string, key: string, userId?: string): boolean {
    const cacheKey = this.generateKey(module, key, userId)
    return this.cache.delete(cacheKey)
  }

  /**
   * Invalidate cache entries based on pattern or options
   */
  invalidate(options: InvalidationOptions): number {
    let deletedCount = 0
    const keysToDelete: string[] = []

    for (const [cacheKey, entry] of this.cache.entries()) {
      let shouldDelete = false

      // Check module filter
      if (options.module && !cacheKey.startsWith(options.module)) {
        continue
      }

      // Check user filter
      if (options.userId && !cacheKey.includes(`:${options.userId}:`)) {
        continue
      }

      // Check pattern filter
      if (options.pattern) {
        const regex = new RegExp(options.pattern)
        shouldDelete = regex.test(cacheKey)
      } else {
        shouldDelete = true
      }

      // Force invalidation ignores TTL
      if (options.force || shouldDelete) {
        keysToDelete.push(cacheKey)
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key)
      deletedCount++
    })

    return deletedCount
  }

  /**
   * Clean up expired entries for a specific module
   */
  private cleanupModule(module: keyof ModuleCacheConfig): number {
    let cleanedCount = 0
    const modulePrefix = `${module}:`
    
    for (const [key, entry] of this.cache.entries()) {
      if (key.startsWith(modulePrefix) && !this.isValid(entry)) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    this.stats.lastCleanup = Date.now()
    return cleanedCount
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const moduleStats = {
      gmail: { emails: 0, threads: 0, details: 0 },
      calendar: { events: 0, lists: 0 },
      meet: { spaces: 0, conferences: 0 },
      aiAgent: { conversations: 0, userBehavior: 0 }
    }

    // Count entries by module and type
    for (const key of this.cache.keys()) {
      if (key.startsWith('gmail:')) {
        if (key.includes(':emails:')) moduleStats.gmail.emails++
        else if (key.includes(':thread:')) moduleStats.gmail.threads++
        else if (key.includes(':detail:')) moduleStats.gmail.details++
      } else if (key.startsWith('calendar:')) {
        if (key.includes(':events:')) moduleStats.calendar.events++
        else if (key.includes(':list:')) moduleStats.calendar.lists++
      } else if (key.startsWith('meet:')) {
        if (key.includes(':spaces:')) moduleStats.meet.spaces++
        else if (key.includes(':conferences:')) moduleStats.meet.conferences++
      } else if (key.startsWith('ai:')) {
        if (key.includes(':conversations:')) moduleStats.aiAgent.conversations++
        else if (key.includes(':behavior:')) moduleStats.aiAgent.userBehavior++
      }
    }

    const totalRequests = this.stats.hits + this.stats.misses
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0
    const missRate = totalRequests > 0 ? (this.stats.misses / totalRequests) * 100 : 0

    return {
      totalEntries: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      memoryUsage: this.getMemoryUsage(),
      lastCleanup: this.stats.lastCleanup,
      moduleStats: this.stats.moduleStats
    }
  }

  /**
   * Estimate memory usage (rough calculation)
   */
  private getMemoryUsage(): number {
    let totalSize = 0
    for (const entry of this.cache.values()) {
      totalSize += JSON.stringify(entry).length * 2 // Rough estimate
    }
    return Math.round(totalSize / 1024) // Return in KB
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
    this.stats.hits = 0
    this.stats.misses = 0
    this.stats.lastCleanup = Date.now()
  }

  /**
   * Destroy cache manager and cleanup intervals
   */
  destroy(): void {
    this.cleanupIntervals.forEach(interval => clearInterval(interval))
    this.cleanupIntervals.clear()
    this.cache.clear()
  }

  /**
   * Manual cleanup of expired entries
   */
  cleanup(): number {
    let cleanedCount = 0
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key)
      cleanedCount++
    })

    this.stats.lastCleanup = now
    return cleanedCount
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ModuleCacheConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // Restart cleanup intervals with new config
    this.cleanupIntervals.forEach(interval => clearInterval(interval))
    this.cleanupIntervals.clear()
    this.initializeCleanupIntervals()
  }
}

// Create singleton instance
export const cacheManager = new UnifiedCacheManager()

// Export the class for testing or custom instances
export { UnifiedCacheManager }
