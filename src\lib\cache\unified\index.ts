/**
 * Unified Cache System - Main Export
 * Centralized cache management for all application modules
 */

// Import required types and manager for utility functions
import { cacheManager } from './cacheManager'
import { CacheOperationResult, ModuleCacheConfig } from './types'

// Core cache manager
export { cacheManager, UnifiedCacheManager } from './cacheManager'

// Type definitions
export type {
  BaseCacheEntry,
  CachedEmail,
  CachedEmailList,
  CachedThread,
  CachedCalendarEvent,
  CachedCalendarList,
  CachedAiConversation,
  CachedUserBehavior,
  CacheConfig,
  ModuleCacheConfig,
  CacheStats,
  CacheOperationResult,
  InvalidationOptions
} from './types'

export {
  CACHE_KEY_PATTERNS,
  DEFAULT_CACHE_CONFIG
} from './types'

// Gmail cache functions
export {
  // Email list caching
  getCachedEmails,
  setCachedEmails,
  isEmailCacheValid,
  
  // Email detail caching
  getCachedEmailDetail,
  setCachedEmailDetail,
  updateCachedEmail,
  
  // Thread caching
  getCachedThread,
  setCachedThread,
  
  // Cache invalidation
  invalidateEmailCache,
  invalidateEmailDetail,
  invalidateThread,
  
  // Bulk operations
  cacheMultipleEmails,
  getMultipleCachedEmails,
  
  // Preloading
  preloadGmailData,
  
  // Statistics and cleanup
  getGmailCacheStats,
  cleanupGmailCache,
  
  // Constants
  GMAIL_CACHE_KEYS,
  GMAIL_CACHE_TTL,
  
  // Legacy compatibility
  getCachedGmailData,
  setCachedGmailData,
  invalidateGmailData
} from './gmailCache'

// Calendar cache functions
export {
  // Calendar event caching
  getCachedCalendarEvents,
  setCachedCalendarEvents,
  isCalendarCacheValid,
  
  // Calendar event detail caching
  getCachedCalendarEvent,
  setCachedCalendarEvent,
  updateCachedCalendarEvent,
  
  // Availability caching
  getCachedAvailability,
  setCachedAvailability,
  
  // Calendar list caching
  getCachedCalendarList,
  setCachedCalendarList,
  
  // Cache invalidation
  invalidateCalendarCache,
  invalidateCalendarEvent,
  invalidateAvailability,
  
  // Bulk operations
  cacheMultipleCalendarEvents,
  getMultipleCachedCalendarEvents,
  
  // Preloading
  preloadCalendarData,
  
  // Statistics and cleanup
  getCalendarCacheStats,
  cleanupCalendarCache,
  
  // Constants
  CALENDAR_CACHE_KEYS,
  CALENDAR_CACHE_TTL,
  
  // Legacy compatibility
  getCachedCalendarData,
  setCachedCalendarData,
  invalidateCalendarData
} from './calendarCache'

// AI Agent cache functions
export {
  // Conversation caching
  getCachedConversation,
  setCachedConversation,
  updateCachedConversation,
  getCachedConversationList,
  setCachedConversationList,

  // User behavior caching
  getCachedUserBehavior,
  setCachedUserBehavior,
  updateCachedUserBehavior,

  // Knowledge graph caching
  getCachedKnowledgeGraph,
  setCachedKnowledgeGraph,

  // AI context caching
  getCachedAiContext,
  setCachedAiContext,

  // Cache invalidation
  invalidateConversationCache,
  invalidateUserBehaviorCache,
  invalidateKnowledgeGraphCache,
  invalidateAiContextCache,

  // Bulk operations
  cacheMultipleConversations,
  getMultipleCachedConversations,

  // Preloading
  preloadAiAgentData,

  // Statistics and cleanup
  getAiAgentCacheStats,
  cleanupAiAgentCache,

  // Constants
  AI_AGENT_CACHE_KEYS,
  AI_AGENT_CACHE_TTL,

  // Legacy compatibility
  getCachedAiData,
  setCachedAiData,
  invalidateAiData
} from './aiAgentCache'

/**
 * Unified Cache Utilities
 * High-level functions that work across all modules
 */

/**
 * Get comprehensive cache statistics for all modules
 */
export function getAllCacheStats() {
  return cacheManager.getStats()
}

/**
 * Clear all cache data across all modules
 */
export function clearAllCache(): void {
  cacheManager.clear()
}

/**
 * Preload data for all modules for a specific user
 */
export async function preloadAllUserData(
  userId: string,
  options: {
    gmail?: boolean
    calendar?: boolean
    aiAgent?: boolean
  } = {}
): Promise<{
  gmail?: CacheOperationResult<any>
  calendar?: CacheOperationResult<any>
  aiAgent?: CacheOperationResult<any>
}> {
  const results: any = {}

  if (options.gmail !== false) {
    const { preloadGmailData } = await import('./gmailCache')
    results.gmail = await preloadGmailData(userId)
  }

  if (options.calendar !== false) {
    const { preloadCalendarData } = await import('./calendarCache')
    results.calendar = await preloadCalendarData(userId)
  }

  if (options.aiAgent !== false) {
    const { preloadAiAgentData } = await import('./aiAgentCache')
    results.aiAgent = await preloadAiAgentData(userId)
  }

  return results
}

/**
 * Invalidate all cache data for a specific user
 */
export function invalidateAllUserCache(userId: string): {
  gmail: number
  calendar: number
  aiAgent: number
} {
  return {
    gmail: cacheManager.invalidate({ module: 'gmail', userId }),
    calendar: cacheManager.invalidate({ module: 'calendar', userId }),
    aiAgent: cacheManager.invalidate({ module: 'aiAgent', userId })
  }
}

/**
 * Update cache configuration for all modules
 */
export function updateCacheConfig(config: Partial<ModuleCacheConfig>): void {
  cacheManager.updateConfig(config)
}

/**
 * Get cache hit rates by module
 */
export function getCacheHitRates(): {
  overall: number
  byModule: {
    gmail: number
    calendar: number
    aiAgent: number
  }
} {
  const stats = cacheManager.getStats()
  
  // This is a simplified calculation - in a real implementation,
  // you'd track hits/misses per module
  return {
    overall: stats.hitRate,
    byModule: {
      gmail: stats.hitRate, // Simplified - would need per-module tracking
      calendar: stats.hitRate,
      aiAgent: stats.hitRate
    }
  }
}

/**
 * Cleanup expired entries across all modules
 */
export function cleanupAllExpiredCache(): {
  gmail: number
  calendar: number
  aiAgent: number
} {
  return {
    gmail: cacheManager.invalidate({ module: 'gmail', force: false }),
    calendar: cacheManager.invalidate({ module: 'calendar', force: false }),
    aiAgent: cacheManager.invalidate({ module: 'aiAgent', force: false })
  }
}

/**
 * Initialize cache system with custom configuration
 */
export function initializeUnifiedCache(config?: Partial<ModuleCacheConfig>): void {
  if (config) {
    cacheManager.updateConfig(config)
  }

  console.log('Unified Cache System initialized')
}

/**
 * Destroy cache system and cleanup resources
 */
export function destroyUnifiedCache(): void {
  cacheManager.destroy()
  console.log('Unified Cache System destroyed')
}
