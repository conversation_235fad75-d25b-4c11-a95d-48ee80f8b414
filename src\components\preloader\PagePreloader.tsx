/**
 * Page-Specific Preloader Component
 * Handles instant navigation by preloading page data before user navigates
 */

'use client'

import React, { useEffect, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { useUnifiedCache } from '@/contexts/cache/UnifiedCacheContext'

interface PagePreloaderProps {
  children: React.ReactNode
  preloadRoutes?: string[]
  preloadOnHover?: boolean
  preloadDelay?: number
}

interface PageData {
  route: string
  data: any
  timestamp: number
  preloaded: boolean
}

export function PagePreloader({
  children,
  preloadRoutes = [
    '/dashboard/gmail',
    '/dashboard/calendar',
    '/dashboard/ai-assistant'
  ],
  preloadOnHover = true,
  preloadDelay = 500
}: PagePreloaderProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { data: session } = useSession()
  const { getCachedData, setCachedData } = useUnifiedCache()

  // Preload data for a specific route
  const preloadRouteData = useCallback(async (route: string) => {
    if (!session?.user?.id) return

    const cacheKey = `page:${route}`
    const cached = getCachedData('aiAgent', cacheKey)
    
    if (cached) {
      console.log(`📋 Page data already cached for: ${route}`)
      return cached
    }

    console.log(`🚀 Preloading page data for: ${route}`)

    try {
      let data = null

      switch (route) {
        case '/dashboard/gmail':
          data = await preloadGmailPageData()
          break
        case '/dashboard/calendar':
          data = await preloadCalendarPageData()
          break

        case '/dashboard/ai-assistant':
          data = await preloadAiAssistantPageData()
          break
        default:
          console.warn(`Unknown route for preloading: ${route}`)
          return null
      }

      if (data) {
        setCachedData('aiAgent', cacheKey, {
          route,
          data,
          timestamp: Date.now(),
          preloaded: true
        })
        console.log(`✅ Page data preloaded and cached for: ${route}`)
      }

      return data
    } catch (error) {
      console.error(`❌ Failed to preload page data for ${route}:`, error)
      return null
    }
  }, [session?.user?.id, getCachedData, setCachedData])

  // Preload Gmail page data
  const preloadGmailPageData = async () => {
    const promises = [
      fetch('/api/gmail/inbox?maxResults=25').then(r => r.ok ? r.json() : null),
      fetch('/api/gmail/labels').then(r => r.ok ? r.json() : null),
      fetch('/api/gmail/drafts').then(r => r.ok ? r.json() : null)
    ]

    const [inbox, labels, drafts] = await Promise.allSettled(promises)

    return {
      inbox: inbox.status === 'fulfilled' ? inbox.value : null,
      labels: labels.status === 'fulfilled' ? labels.value : null,
      drafts: drafts.status === 'fulfilled' ? drafts.value : null
    }
  }

  // Preload Calendar page data
  const preloadCalendarPageData = async () => {
    const today = new Date()
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
    const nextMonth = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)

    const promises = [
      fetch(`/api/calendar?timeMin=${today.toISOString()}&timeMax=${nextWeek.toISOString()}`).then(r => r.ok ? r.json() : null),
      fetch(`/api/calendar?timeMin=${today.toISOString()}&timeMax=${nextMonth.toISOString()}`).then(r => r.ok ? r.json() : null),
      fetch('/api/calendar/colors').then(r => r.ok ? r.json() : null),
      fetch('/api/calendar/calendars').then(r => r.ok ? r.json() : null)
    ]

    const [thisWeek, thisMonth, colors, calendars] = await Promise.allSettled(promises)

    return {
      thisWeek: thisWeek.status === 'fulfilled' ? thisWeek.value : null,
      thisMonth: thisMonth.status === 'fulfilled' ? thisMonth.value : null,
      colors: colors.status === 'fulfilled' ? colors.value : null,
      calendars: calendars.status === 'fulfilled' ? calendars.value : null
    }
  }



  // Preload AI Assistant page data
  const preloadAiAssistantPageData = async () => {
    // For now, return empty data since AI endpoints are not implemented yet
    return {
      conversations: [],
      knowledgeGraph: null
    }
  }

  // Preload all routes on mount
  useEffect(() => {
    if (!session?.user?.id) return

    const preloadAllRoutes = async () => {
      console.log('🚀 Starting page preloading for all routes...')
      
      // Preload routes in order of priority
      const priorityRoutes = preloadRoutes.filter(route => route !== pathname)
      
      for (const route of priorityRoutes) {
        await preloadRouteData(route)
        // Small delay between preloads to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 200))
      }
      
      console.log('✅ All page preloading completed')
    }

    // Start preloading after a short delay to let the current page load first
    const timer = setTimeout(preloadAllRoutes, 2000)
    return () => clearTimeout(timer)
  }, [session?.user?.id, pathname, preloadRoutes, preloadRouteData])

  // Handle link hover preloading
  const handleLinkHover = useCallback((route: string) => {
    if (!preloadOnHover) return

    const timer = setTimeout(() => {
      preloadRouteData(route)
    }, preloadDelay)

    return () => clearTimeout(timer)
  }, [preloadOnHover, preloadDelay, preloadRouteData])

  // Enhanced router push with preloaded data
  const enhancedPush = useCallback((route: string) => {
    const cacheKey = `page:${route}`
    const preloadedData = getCachedData('aiAgent', cacheKey)
    
    if (preloadedData) {
      console.log(`⚡ Using preloaded data for instant navigation to: ${route}`)
      // Data is already cached, navigation will be instant
    } else {
      console.log(`🔄 No preloaded data for: ${route}, will load on navigation`)
    }
    
    router.push(route)
  }, [router, getCachedData])

  // Provide enhanced navigation context
  const navigationContext = {
    preloadRoute: preloadRouteData,
    enhancedPush,
    handleLinkHover,
    getPreloadedData: (route: string) => {
      const cacheKey = `page:${route}`
      return getCachedData('aiAgent', cacheKey)
    }
  }

  return (
    <PagePreloaderContext.Provider value={navigationContext}>
      {children}
    </PagePreloaderContext.Provider>
  )
}

// Context for accessing preloader functionality
const PagePreloaderContext = React.createContext<{
  preloadRoute: (route: string) => Promise<any>
  enhancedPush: (route: string) => void
  handleLinkHover: (route: string) => (() => void) | void
  getPreloadedData: (route: string) => any
} | null>(null)

// Hook for using page preloader
export function usePagePreloader() {
  const context = React.useContext(PagePreloaderContext)
  if (!context) {
    throw new Error('usePagePreloader must be used within a PagePreloader')
  }
  return context
}

// Enhanced Link component with preloading
export function PreloadedLink({
  href,
  children,
  className = '',
  ...props
}: {
  href: string
  children: React.ReactNode
  className?: string
  [key: string]: any
}) {
  const { enhancedPush, handleLinkHover } = usePagePreloader()

  return (
    <button
      className={className}
      onClick={(e) => {
        e.preventDefault()
        enhancedPush(href)
      }}
      onMouseEnter={() => handleLinkHover(href)}
      {...props}
    >
      {children}
    </button>
  )
}

// Page data status indicator
export function PagePreloadStatus({ route }: { route: string }) {
  const { getPreloadedData } = usePagePreloader()
  const preloadedData = getPreloadedData(route)

  if (!preloadedData) return null

  return (
    <div className="inline-flex items-center text-xs text-green-600 ml-2">
      <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
      Preloaded
    </div>
  )
}
