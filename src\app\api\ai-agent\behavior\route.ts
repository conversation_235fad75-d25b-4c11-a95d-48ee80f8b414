import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentService } from '@/lib/chat/aiAgent'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userBehavior = await aiAgentService.getUserBehavior(session.user.id)

    return NextResponse.json({
      behavior: userBehavior,
      success: true
    })

  } catch (error) {
    console.error('Get user behavior error:', error)
    return NextResponse.json(
      { error: 'Failed to get user behavior' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      actionType,
      actionData
    } = await request.json()

    if (!actionType || !actionData) {
      return NextResponse.json({ 
        error: 'Action type and data are required' 
      }, { status: 400 })
    }

    await aiAgentService.trackUserBehavior(session.user.id, actionType, actionData)

    return NextResponse.json({
      message: 'User behavior tracked successfully',
      success: true
    })

  } catch (error) {
    console.error('Track user behavior error:', error)
    return NextResponse.json(
      { error: 'Failed to track user behavior' },
      { status: 500 }
    )
  }
}
