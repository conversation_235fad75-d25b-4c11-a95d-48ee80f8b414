import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unifiedGoogleClient'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'
import { validateSearchParams, validateRequestBody } from "@/lib/validation/utils"
import { calendarListParamsSchema, calendarEventSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'
import {
  convertGoogleEventsListToCachedList,
  generateCalendarEventsCacheKey,
  calculateCalendarStats
} from '@/lib/cache/utils/calendarConversion'

export async function GET(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('calendar-events-fetch', { endpoint: '/api/calendar' })

    const session = await getServerSession(authOptions) as any

    console.log('Calendar API - Session:', session?.user?.id)

    if (!session?.user?.id) {
      console.log('Calendar API - No session or user ID')
      endPerformanceMetric('calendar-events-fetch')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)

    // Validate search parameters using Zod
    const validation = validateSearchParams(searchParams, calendarListParamsSchema)
    if (!validation.success) {
      console.log('Calendar API - Validation error:', validation.error)
      return NextResponse.json({ error: `Invalid parameters: ${validation.error}` }, { status: 400 })
    }

    const { timeMin = new Date().toISOString(), timeMax, maxResults, singleEvents, orderBy } = validation.data

    console.log('Calendar API - Validated params:', { timeMin, timeMax, maxResults })

    // Create cache key based on parameters
    const cacheKey = generateCalendarEventsCacheKey(timeMin, timeMax, maxResults, orderBy)

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedCalendarEvents(
      session.user.id,
      timeMin,
      timeMax,
      'primary'
    )

    if (cachedResult.success && cachedResult.data) {
      console.log('Calendar API - Returning cached data')
      const stats = calculateCalendarStats(cachedResult.data.events)

      // End performance monitoring
      const duration = endPerformanceMetric('calendar-events-fetch')
      if (duration) {
        console.log(`⚡ Cache hit - calendar fetch: ${duration.toFixed(2)}ms for user ${session.user.id}`)
      }

      return NextResponse.json({
        events: cachedResult.data.events,
        stats,
        count: cachedResult.data.totalCount,
        cached: true
      })
    }

    try {
      console.log('Calendar API - Getting calendar client for user:', session.user.id)
      const { calendar } = await getCalendarClient(session.user.id)

      const listParams: any = {
        calendarId: 'primary',
        timeMin: timeMin,
        maxResults: maxResults,
        singleEvents: singleEvents,
        orderBy: orderBy,
      }

      if (timeMax) {
        listParams.timeMax = timeMax
      }

      console.log('Calendar API - Calling calendar.events.list with params:', listParams)
      const response = await calendar.events.list(listParams)
      const events = response.data.items || []

      console.log('Calendar API - Success, found', events.length, 'events')

      // Convert and cache the events
      if (events.length > 0) {
        const cachedEventsList = convertGoogleEventsListToCachedList(
          events,
          timeMin,
          timeMax,
          'primary'
        )

        unifiedCacheService.cacheCalendarEvents(
          session.user.id,
          cachedEventsList,
          timeMin,
          timeMax,
          'primary'
        )
        console.log(`📅 Cached ${events.length} calendar events for user: ${session.user.id}`)
      }

      // Calculate basic stats
      const stats = calculateCalendarStats(
        events.map((event: any) => ({
          id: event.id,
          summary: event.summary || '',
          start: event.start,
          end: event.end
        }))
      )

      // End performance monitoring
      const duration = endPerformanceMetric('calendar-events-fetch')
      if (duration && duration > 2000) {
        console.warn(`Slow calendar fetch: ${duration.toFixed(2)}ms for user ${session.user.id}`)
      }

      return NextResponse.json({
        events: events,
        stats: stats,
        count: events.length,
        nextPageToken: response.data.nextPageToken,
        summary: response.data.summary
      })

    } catch (error) {
      endPerformanceMetric('calendar-events-fetch')
      console.error('Google Calendar API error:', error)

      // Check if it's a connection/auth error
      if (error instanceof Error) {
        if (error.message.includes('not connected') ||
            error.message.includes('insufficient authentication') ||
            error.message.includes('invalid_grant')) {
          return NextResponse.json(
            { error: 'Calendar not connected. Please reconnect your Google Calendar.' },
            { status: 403 }
          )
        }
      }

      return NextResponse.json(
        { error: 'Failed to fetch calendar events. Please try again.' },
        { status: 500 }
      )
    }

  } catch (error) {
    endPerformanceMetric('calendar-events-fetch')
    console.error('Calendar fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('calendar-event-create', { endpoint: '/api/calendar' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.id) {
      endPerformanceMetric('calendar-event-create')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate request body using Zod
    const validation = await validateRequestBody(request, calendarEventSchema)
    if (!validation.success) {
      console.log('Calendar API POST - Validation error:', validation.error)
      return NextResponse.json({ error: `Invalid event data: ${validation.error}` }, { status: 400 })
    }

    const eventData = validation.data
    console.log('Calendar API POST - Validated request body:', eventData)

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Map validated event data to Google Calendar format
      const googleEvent: any = {
        summary: eventData.summary,
        description: eventData.description,
        start: {
          dateTime: eventData.start.dateTime,
          date: eventData.start.date,
          timeZone: eventData.start.timeZone || 'UTC',
        },
        end: {
          dateTime: eventData.end.dateTime,
          date: eventData.end.date,
          timeZone: eventData.end.timeZone || 'UTC',
        },
        location: eventData.location,
        attendees: eventData.attendees?.map(attendee => ({
          email: attendee.email,
          displayName: attendee.displayName,
          responseStatus: attendee.responseStatus || 'needsAction'
        })) || [],
        recurrence: eventData.recurrence,
        reminders: eventData.reminders || {
          useDefault: true
        }
      }

      // All-day events are handled by the date field in start/end objects

      // Conference data can be added through the Google Calendar API if needed

      const insertParams: any = {
        calendarId: 'primary',
        requestBody: googleEvent,
        sendUpdates: 'all'
      }

      // Conference data version can be set if needed
      // insertParams.conferenceDataVersion = 1

      const response = await calendar.events.insert(insertParams)

      // Invalidate calendar cache after creating event
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'events:list'
      })

      // End performance monitoring
      const duration = endPerformanceMetric('calendar-event-create')
      if (duration && duration > 3000) {
        console.warn(`Slow calendar event creation: ${duration.toFixed(2)}ms for user ${session.user.id}`)
      }

      return NextResponse.json({
        event: response.data,
        message: 'Event created successfully'
      })

    } catch (error) {
      endPerformanceMetric('calendar-event-create')
      console.error('Google Calendar create error:', error)
      return NextResponse.json(
        { error: 'Failed to create calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    endPerformanceMetric('calendar-event-create')
    console.error('Calendar create error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('calendar-event-update', { endpoint: '/api/calendar' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.id) {
      endPerformanceMetric('calendar-event-update')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    const eventData = await request.json()

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Get existing event first
      const existingEvent = await calendar.events.get({
        calendarId: 'primary',
        eventId: eventId
      })

      // Update the event with new data
      const updatedEvent = {
        ...existingEvent.data,
        summary: eventData.summary || eventData.title || existingEvent.data.summary,
        description: eventData.description !== undefined ? eventData.description : existingEvent.data.description,
        location: eventData.location !== undefined ? eventData.location : existingEvent.data.location,
      }

      if (eventData.startDate || eventData.start) {
        updatedEvent.start = {
          dateTime: eventData.startDate || eventData.start?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      if (eventData.endDate || eventData.end) {
        updatedEvent.end = {
          dateTime: eventData.endDate || eventData.end?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      if (eventData.attendees) {
        updatedEvent.attendees = eventData.attendees.filter((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return email && email.trim() && email.includes('@')
        }).map((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return { email: email.trim() }
        })
      }

      const response = await calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: updatedEvent,
        sendUpdates: 'all'
      })

      // Invalidate calendar cache after updating event
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'events:list'
      })

      // End performance monitoring
      const duration = endPerformanceMetric('calendar-event-update')
      if (duration && duration > 3000) {
        console.warn(`Slow calendar event update: ${duration.toFixed(2)}ms for user ${session.user.id}`)
      }

      return NextResponse.json({
        event: response.data,
        message: 'Event updated successfully'
      })

    } catch (error) {
      endPerformanceMetric('calendar-event-update')
      console.error('Google Calendar update error:', error)
      return NextResponse.json(
        { error: 'Failed to update calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    endPerformanceMetric('calendar-event-update')
    console.error('Calendar update error:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar event' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('calendar-event-delete', { endpoint: '/api/calendar' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.id) {
      endPerformanceMetric('calendar-event-delete')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      await calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all'
      })

      // Invalidate calendar cache after deleting event
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'events:list'
      })

      // End performance monitoring
      const duration = endPerformanceMetric('calendar-event-delete')
      if (duration && duration > 2000) {
        console.warn(`Slow calendar event deletion: ${duration.toFixed(2)}ms for user ${session.user.id}`)
      }

      return NextResponse.json({
        message: 'Event deleted successfully'
      })

    } catch (error) {
      endPerformanceMetric('calendar-event-delete')
      console.error('Google Calendar delete error:', error)
      return NextResponse.json(
        { error: 'Failed to delete calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    endPerformanceMetric('calendar-event-delete')
    console.error('Calendar delete error:', error)
    return NextResponse.json(
      { error: 'Failed to delete calendar event' },
      { status: 500 }
    )
  }
}