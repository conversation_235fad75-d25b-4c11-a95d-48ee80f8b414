import { NextRequest } from "next/server"
import {
  bulkEmailOperations,
  withGmailAuth,
  formatBulkOperationResult
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { bulkEmailActionSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-bulk-modify', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, bulkEmailActionSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-bulk-modify')
      throw new Error(`Invalid bulk action data: ${validation.error}`)
    }

    const { emailIds, addLabels, removeLabels } = validation.data

    let totalOperations = 0
    let successfulOperations = 0
    let failedOperations = 0

    // Handle adding labels
    for (const labelId of addLabels || []) {
      const result = await bulkEmailOperations(user.id, emailIds, 'addLabel', labelId)
      totalOperations += emailIds.length
      successfulOperations += result.success
      failedOperations += result.failed

      if (result.failed > 0) {
        console.warn(`Failed to add label ${labelId} to ${result.failed} emails`)
      }
    }

    // Handle removing labels
    for (const labelId of removeLabels || []) {
      const result = await bulkEmailOperations(user.id, emailIds, 'removeLabel', labelId)
      totalOperations += emailIds.length
      successfulOperations += result.success
      failedOperations += result.failed

      if (result.failed > 0) {
        console.warn(`Failed to remove label ${labelId} from ${result.failed} emails`)
      }
    }

    // End performance monitoring
    endPerformanceMetric('gmail-bulk-modify')

    // If no operations were performed, just return success for the email count
    if (totalOperations === 0) {
      return {
        success: true,
        message: `No label operations performed on ${emailIds.length} email(s)`,
        stats: {
          total: emailIds.length,
          success: emailIds.length,
          failed: 0
        }
      }
    }

    return formatBulkOperationResult(
      totalOperations,
      successfulOperations,
      failedOperations,
      'modified'
    )
  })
}