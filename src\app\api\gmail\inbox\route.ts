import { NextRequest } from "next/server"
import { getInboxEmails, with<PERSON>mail<PERSON><PERSON> } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"
import { unifiedCacheService } from "@/lib/cache/unifiedCacheService"
import { CachedEmailList } from "@/lib/cache/unified/types"
import { convertInboxEmailToCachedEmail, generateEmailListCacheKey } from "@/lib/cache/utils/emailConversion"

export async function GET(request: NextRequest) {
  return withG<PERSON>A<PERSON>(request, async ({ user, url }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-inbox-fetch', { userId: user.id })

    try {
      // Validate search parameters using Zod
      const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
      if (!validation.success) {
        throw new Error(`Invalid parameters: ${validation.error}`)
      }

      const params = validation.data

      // Create cache key based on parameters
      const cacheKey = generateEmailListCacheKey(
        'inbox',
        params.limit,
        params.pageToken,
        params.labelId,
        params.dateFrom,
        params.dateTo
      )

      // Try to get from cache first
      const cachedResult = unifiedCacheService.getCachedEmailList(user.id, 'inbox', cacheKey)
      if (cachedResult.success && cachedResult.data) {
        console.log('📧 Returning cached inbox emails for user:', user.id)

        // End performance monitoring
        const duration = endPerformanceMetric('gmail-inbox-fetch')
        if (duration) {
          console.log(`⚡ Cache hit - inbox fetch: ${duration.toFixed(2)}ms for user ${user.id}`)
        }

        return {
          emails: cachedResult.data.emails,
          totalCount: cachedResult.data.totalCount,
          nextPageToken: cachedResult.data.nextPageToken,
          cached: true
        }
      }

      // Fetch emails from Gmail with validated parameters
      const result = await getInboxEmails(
        user.id,
        params.limit,
        params.pageToken,
        params.page,
        params.labelId,
        params.dateFrom ? new Date(params.dateFrom) : undefined,
        params.dateTo ? new Date(params.dateTo) : undefined
      )

      // Cache the result if successful
      if (result.emails && result.emails.length > 0) {
        const emailListToCache: CachedEmailList = {
          emails: result.emails.map(convertInboxEmailToCachedEmail),
          totalCount: result.totalCount || result.emails.length,
          nextPageToken: result.nextPageToken,
          timestamp: Date.now()
        }

        unifiedCacheService.cacheEmailList(user.id, emailListToCache, 'inbox', cacheKey)
        console.log(`📧 Cached ${result.emails.length} inbox emails for user: ${user.id}`)
      }

      // End performance monitoring
      const duration = endPerformanceMetric('gmail-inbox-fetch')
      if (duration && duration > 3000) {
        console.warn(`Slow inbox fetch: ${duration.toFixed(2)}ms for user ${user.id}`)
      }

      return result
    } catch (error) {
      // End performance monitoring on error
      endPerformanceMetric('gmail-inbox-fetch')
      throw error
    }
  })
}