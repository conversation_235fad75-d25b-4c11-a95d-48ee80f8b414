/**
 * AI Agent Cache Helper - Unified Cache System
 * AI Agent-specific caching functions using the unified cache manager
 */

import { cacheManager } from './cacheManager'
import {
  CachedAiConversation,
  CachedUserBehavior,
  CacheOperationResult
} from './types'

const MODULE_NAME = 'aiAgent'

/**
 * AI Conversation Caching
 */
export function getCachedConversation(
  userId: string,
  conversationId: string
): CacheOperationResult<CachedAiConversation> {
  const key = `conversation:${conversationId}`
  return cacheManager.get<CachedAiConversation>(MODULE_NAME, key, userId)
}

export function setCachedConversation(
  userId: string,
  conversation: CachedAiConversation
): CacheOperationResult<CachedAiConversation> {
  const key = `conversation:${conversation.id}`
  return cacheManager.set(MODULE_NAME, key, conversation, userId)
}

export function updateCachedConversation(
  userId: string,
  conversationId: string,
  updates: Partial<CachedAiConversation>
): CacheOperationResult<CachedAiConversation> {
  const existing = getCachedConversation(userId, conversationId)
  if (existing.success && existing.data) {
    const updatedConversation = { ...existing.data, ...updates }
    return setCachedConversation(userId, updatedConversation)
  }
  return {
    success: false,
    error: 'Conversation not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * AI Conversation List Caching
 */
export function getCachedConversationList(
  userId: string,
  limit?: number,
  offset?: number
): CacheOperationResult<CachedAiConversation[]> {
  const key = `conversations:list:${limit || 'all'}:${offset || '0'}`
  return cacheManager.get<CachedAiConversation[]>(MODULE_NAME, key, userId)
}

export function setCachedConversationList(
  userId: string,
  conversations: CachedAiConversation[],
  limit?: number,
  offset?: number
): CacheOperationResult<CachedAiConversation[]> {
  const key = `conversations:list:${limit || 'all'}:${offset || '0'}`
  return cacheManager.set(MODULE_NAME, key, conversations, userId)
}

/**
 * User Behavior Caching
 */
export function getCachedUserBehavior(
  userId: string
): CacheOperationResult<CachedUserBehavior> {
  const key = 'behavior:patterns'
  return cacheManager.get<CachedUserBehavior>(MODULE_NAME, key, userId)
}

export function setCachedUserBehavior(
  userId: string,
  behavior: CachedUserBehavior
): CacheOperationResult<CachedUserBehavior> {
  const key = 'behavior:patterns'
  return cacheManager.set(MODULE_NAME, key, behavior, userId)
}

export function updateCachedUserBehavior(
  userId: string,
  updates: Partial<CachedUserBehavior>
): CacheOperationResult<CachedUserBehavior> {
  const existing = getCachedUserBehavior(userId)
  if (existing.success && existing.data) {
    const updatedBehavior = { ...existing.data, ...updates }
    return setCachedUserBehavior(userId, updatedBehavior)
  }
  return {
    success: false,
    error: 'User behavior not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * Knowledge Graph Caching
 */
export function getCachedKnowledgeGraph(
  userId: string,
  graphType?: string
): CacheOperationResult<any> {
  const key = `knowledge:graph:${graphType || 'main'}`
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedKnowledgeGraph(
  userId: string,
  knowledgeGraph: any,
  graphType?: string
): CacheOperationResult<any> {
  const key = `knowledge:graph:${graphType || 'main'}`
  return cacheManager.set(MODULE_NAME, key, knowledgeGraph, userId)
}

/**
 * AI Context Caching
 */
export function getCachedAiContext(
  userId: string,
  contextType: string
): CacheOperationResult<any> {
  const key = `context:${contextType}`
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedAiContext(
  userId: string,
  context: any,
  contextType: string
): CacheOperationResult<any> {
  const key = `context:${contextType}`
  return cacheManager.set(MODULE_NAME, key, context, userId)
}

/**
 * Cache Invalidation
 */
export function invalidateConversationCache(
  userId: string,
  conversationId?: string
): number {
  const pattern = conversationId ? `conversation:${conversationId}` : 'conversation'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

export function invalidateUserBehaviorCache(userId: string): boolean {
  return cacheManager.delete(MODULE_NAME, 'behavior:patterns', userId)
}

export function invalidateKnowledgeGraphCache(
  userId: string,
  graphType?: string
): boolean {
  const key = `knowledge:graph:${graphType || 'main'}`
  return cacheManager.delete(MODULE_NAME, key, userId)
}

export function invalidateAiContextCache(
  userId: string,
  contextType?: string
): number {
  const pattern = contextType ? `context:${contextType}` : 'context'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

/**
 * Bulk Operations
 */
export function cacheMultipleConversations(
  userId: string,
  conversations: CachedAiConversation[]
): CacheOperationResult<CachedAiConversation[]> {
  const results: CacheOperationResult<CachedAiConversation>[] = []
  
  conversations.forEach(conversation => {
    const result = setCachedConversation(userId, conversation)
    results.push(result)
  })

  const allSuccessful = results.every(r => r.success)
  
  return {
    success: allSuccessful,
    data: allSuccessful ? conversations : undefined,
    error: allSuccessful ? undefined : 'Some conversations failed to cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

export function getMultipleCachedConversations(
  userId: string,
  conversationIds: string[]
): CacheOperationResult<CachedAiConversation[]> {
  const conversations: CachedAiConversation[] = []
  const notFound: string[] = []

  conversationIds.forEach(conversationId => {
    const result = getCachedConversation(userId, conversationId)
    if (result.success && result.data) {
      conversations.push(result.data)
    } else {
      notFound.push(conversationId)
    }
  })

  return {
    success: notFound.length === 0,
    data: conversations,
    error: notFound.length > 0 ? `Conversations not found: ${notFound.join(', ')}` : undefined,
    fromCache: true,
    timestamp: Date.now()
  }
}

/**
 * Preloading
 */
export async function preloadAiAgentData(
  userId: string,
  options: {
    loadRecentConversations?: boolean
    loadUserBehavior?: boolean
    loadKnowledgeGraph?: boolean
    conversationLimit?: number
  } = {}
): Promise<CacheOperationResult<any>> {
  try {
    const preloadTasks: Promise<any>[] = []

    if (options.loadRecentConversations !== false) {
      console.log(`Preloading recent conversations for user ${userId}`)
    }

    if (options.loadUserBehavior !== false) {
      console.log(`Preloading user behavior patterns for user ${userId}`)
    }

    if (options.loadKnowledgeGraph !== false) {
      console.log(`Preloading knowledge graph for user ${userId}`)
    }

    await Promise.all(preloadTasks)

    return {
      success: true,
      data: { preloaded: true },
      fromCache: false,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Preload failed',
      fromCache: false,
      timestamp: Date.now()
    }
  }
}

/**
 * Statistics and Cleanup
 */
export function getAiAgentCacheStats() {
  const globalStats = cacheManager.getStats()
  return {
    ...globalStats.moduleStats.aiAgent,
    hitRate: globalStats.hitRate,
    missRate: globalStats.missRate,
    totalEntries: globalStats.totalEntries,
    memoryUsage: globalStats.memoryUsage
  }
}

export function cleanupAiAgentCache(userId?: string): number {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    force: false // Only cleanup expired entries
  })
}

/**
 * Constants
 */
export const AI_AGENT_CACHE_KEYS = {
  CONVERSATIONS: 'conversations',
  CONVERSATION: 'conversation',
  USER_BEHAVIOR: 'behavior:patterns',
  KNOWLEDGE_GRAPH: 'knowledge:graph',
  CONTEXT: 'context'
} as const

export const AI_AGENT_CACHE_TTL = 30 * 60 * 1000 // 30 minutes

/**
 * Legacy compatibility functions
 */
export function getCachedAiData(userId: string, key: string) {
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedAiData(userId: string, key: string, data: any) {
  return cacheManager.set(MODULE_NAME, key, data, userId)
}

export function invalidateAiData(userId: string, pattern?: string) {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}
