/**
 * Gmail Cache Helper - Unified Cache System
 * Gmail-specific caching functions using the unified cache manager
 */

import { cacheManager } from './cacheManager'
import {
  CachedEmail,
  CachedEmailList,
  CachedThread,
  CacheOperationResult
} from './types'

const MODULE_NAME = 'gmail'

/**
 * Email List Caching
 */
export function getCachedEmails(
  userId: string,
  query?: string,
  category?: string
): CacheOperationResult<CachedEmailList> {
  const key = `emails:list:${query || 'inbox'}:${category || 'all'}`
  return cacheManager.get<CachedEmailList>(MODULE_NAME, key, userId)
}

export function setCachedEmails(
  userId: string,
  emails: CachedEmailList,
  query?: string,
  category?: string
): CacheOperationResult<CachedEmailList> {
  const key = `emails:list:${query || 'inbox'}:${category || 'all'}`
  return cacheManager.set(MODULE_NAME, key, emails, userId)
}

export function isEmailCacheValid(
  userId: string,
  query?: string,
  category?: string
): boolean {
  const result = getCachedEmails(userId, query, category)
  return result.success && result.fromCache
}

/**
 * Email Detail Caching
 */
export function getCachedEmailDetail(
  userId: string,
  emailId: string
): CacheOperationResult<CachedEmail> {
  const key = `email:detail:${emailId}`
  return cacheManager.get<CachedEmail>(MODULE_NAME, key, userId)
}

export function setCachedEmailDetail(
  userId: string,
  email: CachedEmail
): CacheOperationResult<CachedEmail> {
  const key = `email:detail:${email.id}`
  return cacheManager.set(MODULE_NAME, key, email, userId)
}

export function updateCachedEmail(
  userId: string,
  emailId: string,
  updates: Partial<CachedEmail>
): CacheOperationResult<CachedEmail> {
  const existing = getCachedEmailDetail(userId, emailId)
  if (existing.success && existing.data) {
    const updatedEmail = { ...existing.data, ...updates }
    return setCachedEmailDetail(userId, updatedEmail)
  }
  return {
    success: false,
    error: 'Email not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * Thread Caching
 */
export function getCachedThread(
  userId: string,
  threadId: string
): CacheOperationResult<CachedThread> {
  const key = `thread:${threadId}`
  return cacheManager.get<CachedThread>(MODULE_NAME, key, userId)
}

export function setCachedThread(
  userId: string,
  thread: CachedThread
): CacheOperationResult<CachedThread> {
  const key = `thread:${thread.id}`
  return cacheManager.set(MODULE_NAME, key, thread, userId)
}

/**
 * Cache Invalidation
 */
export function invalidateEmailCache(userId: string, query?: string): number {
  const pattern = query ? `emails:list:${query}` : 'emails:list'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

export function invalidateEmailDetail(userId: string, emailId: string): boolean {
  return cacheManager.delete(MODULE_NAME, `email:detail:${emailId}`, userId)
}

export function invalidateThread(userId: string, threadId: string): boolean {
  return cacheManager.delete(MODULE_NAME, `thread:${threadId}`, userId)
}

/**
 * Bulk Operations
 */
export function cacheMultipleEmails(
  userId: string,
  emails: CachedEmail[]
): CacheOperationResult<CachedEmail[]> {
  const results: CacheOperationResult<CachedEmail>[] = []
  
  emails.forEach(email => {
    const result = setCachedEmailDetail(userId, email)
    results.push(result)
  })

  const allSuccessful = results.every(r => r.success)
  
  return {
    success: allSuccessful,
    data: allSuccessful ? emails : undefined,
    error: allSuccessful ? undefined : 'Some emails failed to cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

export function getMultipleCachedEmails(
  userId: string,
  emailIds: string[]
): CacheOperationResult<CachedEmail[]> {
  const emails: CachedEmail[] = []
  const notFound: string[] = []

  emailIds.forEach(emailId => {
    const result = getCachedEmailDetail(userId, emailId)
    if (result.success && result.data) {
      emails.push(result.data)
    } else {
      notFound.push(emailId)
    }
  })

  return {
    success: notFound.length === 0,
    data: emails,
    error: notFound.length > 0 ? `Emails not found: ${notFound.join(', ')}` : undefined,
    fromCache: true,
    timestamp: Date.now()
  }
}

/**
 * Preloading
 */
export async function preloadGmailData(
  userId: string,
  options: {
    loadInbox?: boolean
    loadSent?: boolean
    loadDrafts?: boolean
    loadImportant?: boolean
    emailCount?: number
  } = {}
): Promise<CacheOperationResult<any>> {
  try {
    const preloadTasks: Promise<any>[] = []

    if (options.loadInbox !== false) {
      // This would typically call the actual Gmail API
      // For now, we'll just mark the intention
      console.log(`Preloading inbox for user ${userId}`)
    }

    if (options.loadSent) {
      console.log(`Preloading sent emails for user ${userId}`)
    }

    if (options.loadDrafts) {
      console.log(`Preloading drafts for user ${userId}`)
    }

    if (options.loadImportant) {
      console.log(`Preloading important emails for user ${userId}`)
    }

    await Promise.all(preloadTasks)

    return {
      success: true,
      data: { preloaded: true },
      fromCache: false,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Preload failed',
      fromCache: false,
      timestamp: Date.now()
    }
  }
}

/**
 * Statistics and Cleanup
 */
export function getGmailCacheStats() {
  const globalStats = cacheManager.getStats()
  return {
    ...globalStats.moduleStats.gmail,
    hitRate: globalStats.hitRate,
    missRate: globalStats.missRate,
    totalEntries: globalStats.totalEntries,
    memoryUsage: globalStats.memoryUsage
  }
}

export function cleanupGmailCache(userId?: string): number {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    force: false // Only cleanup expired entries
  })
}

/**
 * Constants
 */
export const GMAIL_CACHE_KEYS = {
  EMAIL_LIST: 'emails:list',
  EMAIL_DETAIL: 'email:detail',
  THREAD: 'thread',
  LABELS: 'labels',
  DRAFTS: 'drafts'
} as const

export const GMAIL_CACHE_TTL = 2 * 60 * 1000 // 2 minutes

/**
 * Legacy compatibility functions
 * These maintain compatibility with existing Gmail cache usage
 */
export function getCachedGmailData(userId: string, key: string) {
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedGmailData(userId: string, key: string, data: any) {
  return cacheManager.set(MODULE_NAME, key, data, userId)
}

export function invalidateGmailData(userId: string, pattern?: string) {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}
