"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"

import {
  Mail,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  MapPin,
  Users,
  Star,
  Paperclip,
  RefreshCw,
  ExternalLink
} from "lucide-react"
import Link from "next/link"
import { format, parseISO, isToday, isTomorrow, isThisWeek } from "date-fns"

interface UpcomingEvent {
  id: string
  summary: string
  start: {
    dateTime?: string
    date?: string
  }
  end: {
    dateTime?: string
    date?: string
  }
  location?: string
  attendees?: Array<{ email: string }>
  htmlLink?: string
}

interface TodayEmail {
  id: string
  threadId: string
  from: string
  subject: string
  snippet: string
  date: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
}

export default function Dashboard() {
  const { data: session } = useSession()
  const [currentTime, setCurrentTime] = useState(new Date())
  const [upcomingEvents, setUpcomingEvents] = useState<UpcomingEvent[]>([])
  const [todayEmails, setTodayEmails] = useState<TodayEmail[]>([])
  const [loading, setLoading] = useState(true)
  const [eventsLoading, setEventsLoading] = useState(true)
  const [emailsLoading, setEmailsLoading] = useState(true)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const fetchUpcomingEvents = async () => {
    try {
      setEventsLoading(true)
      const response = await fetch('/api/calendar/events?upcoming=true&limit=5')
      if (response.ok) {
        const events = await response.json()
        setUpcomingEvents(events)
      }
    } catch (error) {
      console.error('Error fetching events:', error)
    } finally {
      setEventsLoading(false)
    }
  }

  const fetchTodayEmails = async () => {
    try {
      setEmailsLoading(true)
      const today = new Date().toISOString().split('T')[0]
      const response = await fetch(`/api/gmail/emails?date=${today}&limit=5`)
      if (response.ok) {
        const emails = await response.json()
        setTodayEmails(emails.emails || [])
      }
    } catch (error) {
      console.error('Error fetching emails:', error)
    } finally {
      setEmailsLoading(false)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([
        fetchUpcomingEvents(),
        fetchTodayEmails()
      ])
      setLoading(false)
    }
    loadData()
  }, [])

  const formatEventTime = (event: UpcomingEvent) => {
    if (event.start.dateTime) {
      const startDate = parseISO(event.start.dateTime)
      const endDate = parseISO(event.end.dateTime!)
      
      if (isToday(startDate)) {
        return `Today, ${format(startDate, 'h:mm a')} - ${format(endDate, 'h:mm a')}`
      } else if (isTomorrow(startDate)) {
        return `Tomorrow, ${format(startDate, 'h:mm a')} - ${format(endDate, 'h:mm a')}`
      } else if (isThisWeek(startDate)) {
        return `${format(startDate, 'EEEE, h:mm a')} - ${format(endDate, 'h:mm a')}`
      } else {
        return `${format(startDate, 'MMM d, h:mm a')} - ${format(endDate, 'h:mm a')}`
      }
    } else {
      const date = parseISO(event.start.date!)
      if (isToday(date)) {
        return 'Today (All day)'
      } else if (isTomorrow(date)) {
        return 'Tomorrow (All day)'
      } else {
        return `${format(date, 'MMM d')} (All day)`
      }
    }
  }

  const formatEmailTime = (dateString: string) => {
    const date = parseISO(dateString)
    return format(date, 'h:mm a')
  }

  const getEmailSender = (from: string) => {
    const match = from.match(/^(.+?)\s*<(.+)>$/)
    if (match) {
      return match[1].trim().replace(/"/g, '')
    }
    return from.split('@')[0]
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Time Block Skeleton */}
            <Card className="lg:col-span-1">
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
              </CardContent>
            </Card>
            
            {/* Events Skeleton */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            {/* Emails Skeleton */}
            <Card className="lg:col-span-1">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              Welcome back, {session?.user?.name?.split(' ')[0]}! 👋
            </h1>
            <p className="text-slate-600 text-lg mt-2">
              Your personalized dashboard overview
            </p>
          </div>
          <Button 
            variant="outline" 
            onClick={() => {
              fetchUpcomingEvents()
              fetchTodayEmails()
            }}
            disabled={loading}
            className="bg-white/80 backdrop-blur-sm border-slate-200 hover:bg-white"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Time Block with Background Image */}
          <Card className="lg:col-span-1 overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 border-0 shadow-xl">
            <CardContent className="p-0 relative">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100" height="100" fill="url(#grid)" />
                </svg>
              </div>
              
              <div className="relative p-8 text-white">
                <div className="flex items-center justify-between mb-6">
                  <Clock className="h-8 w-8 text-white/80" />
                  <Badge variant="secondary" className="bg-white/20 text-white border-0">
                    Live
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="text-4xl font-bold font-mono">
                    {format(currentTime, 'HH:mm:ss')}
                  </div>
                  <div className="text-lg text-white/90">
                    {format(currentTime, 'EEEE, MMMM d, yyyy')}
                  </div>
                  <div className="text-sm text-white/70">
                    {format(currentTime, 'zzz')}
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-white/20">
                  <div className="text-sm text-white/80">
                    Good {format(currentTime, 'a') === 'AM' ? 'morning' : 'evening'}, {session?.user?.name?.split(' ')[0]}!
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Events */}
          <Card className="lg:col-span-1 bg-white/80 backdrop-blur-sm border-slate-200 shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-indigo-600" />
                  <CardTitle className="text-slate-800">Upcoming Events</CardTitle>
                </div>
                <Link href="/dashboard/calendar">
                  <Button variant="ghost" size="sm" className="text-indigo-600 hover:text-indigo-700">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {eventsLoading ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-16 bg-slate-100 rounded-lg animate-pulse"></div>
                  ))}
                </div>
              ) : upcomingEvents.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-slate-300 mx-auto mb-3" />
                  <p className="text-slate-500 text-sm">No upcoming events</p>
                  <Link href="/dashboard/calendar/new-event">
                    <Button variant="outline" size="sm" className="mt-3">
                      Create Event
                    </Button>
                  </Link>
                </div>
              ) : (
                upcomingEvents.slice(0, 4).map((event) => (
                  <div key={event.id} className="group p-3 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50/50 transition-all duration-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-slate-800 truncate group-hover:text-indigo-700">
                          {event.summary}
                        </h4>
                        <div className="flex items-center space-x-1 mt-1">
                          <Clock className="h-3 w-3 text-slate-400" />
                          <span className="text-xs text-slate-500">
                            {formatEventTime(event)}
                          </span>
                        </div>
                        {event.location && (
                          <div className="flex items-center space-x-1 mt-1">
                            <MapPin className="h-3 w-3 text-slate-400" />
                            <span className="text-xs text-slate-500 truncate">
                              {event.location}
                            </span>
                          </div>
                        )}
                        {event.attendees && event.attendees.length > 0 && (
                          <div className="flex items-center space-x-1 mt-1">
                            <Users className="h-3 w-3 text-slate-400" />
                            <span className="text-xs text-slate-500">
                              {event.attendees.length} attendee{event.attendees.length > 1 ? 's' : ''}
                            </span>
                          </div>
                        )}
                      </div>
                      {event.htmlLink && (
                        <a 
                          href={event.htmlLink} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <ExternalLink className="h-4 w-4 text-slate-400 hover:text-indigo-600" />
                        </a>
                      )}
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* Latest Today's Mails */}
          <Card className="lg:col-span-1 bg-white/80 backdrop-blur-sm border-slate-200 shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-emerald-600" />
                  <CardTitle className="text-slate-800">Today's Emails</CardTitle>
                </div>
                <Link href="/dashboard/emails">
                  <Button variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {emailsLoading ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-16 bg-slate-100 rounded-lg animate-pulse"></div>
                  ))}
                </div>
              ) : todayEmails.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 text-slate-300 mx-auto mb-3" />
                  <p className="text-slate-500 text-sm">No emails today</p>
                  <p className="text-slate-400 text-xs mt-1">Check back later for new messages</p>
                </div>
              ) : (
                todayEmails.slice(0, 4).map((email) => (
                  <div key={email.id} className="group p-3 rounded-lg border border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all duration-200">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          email.isRead ? 'bg-slate-300' : 'bg-emerald-500'
                        }`}></div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`text-sm truncate group-hover:text-emerald-700 ${
                            email.isRead ? 'font-normal text-slate-600' : 'font-semibold text-slate-800'
                          }`}>
                            {getEmailSender(email.from)}
                          </h4>
                          <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                            {email.isStarred && (
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            )}
                            {email.hasAttachments && (
                              <Paperclip className="h-3 w-3 text-slate-400" />
                            )}
                            <span className="text-xs text-slate-500">
                              {formatEmailTime(email.date)}
                            </span>
                          </div>
                        </div>
                        <p className={`text-sm truncate mt-1 ${
                          email.isRead ? 'text-slate-500' : 'text-slate-700'
                        }`}>
                          {email.subject}
                        </p>
                        <p className="text-xs text-slate-400 truncate mt-1">
                          {email.snippet}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}