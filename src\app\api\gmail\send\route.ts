import { NextRequest } from "next/server"
import {
  sendEmail,
  with<PERSON><PERSON><PERSON><PERSON>,
  shouldResetDailyCount
} from "@/lib/gmail"
import { prisma } from "@/lib/prisma"
import { validateRequestBody } from "@/lib/validation/utils"
import { emailComposeSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-send-email', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, emailComposeSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-send-email')
      throw new Error(`Invalid email data: ${validation.error}`)
    }

    const body = validation.data

    const { to, subject, htmlBody, textBody, campaignId, contactId, attachments } = body

    // Extract primary recipient email for contact management
    const primaryRecipientEmail = to[0]?.email

    // Handle contact management
    let contact = null
    if (contactId) {
      contact = await prisma.contact.findFirst({
        where: {
          id: contactId,
          userId: user.id
        }
      })
      if (!contact) {
        throw new Error("Contact not found or doesn't belong to user")
      }
    } else if (primaryRecipientEmail) {
      // If no contactId provided, find or create contact with this email
      contact = await prisma.contact.findFirst({
        where: {
          email: primaryRecipientEmail,
          userId: user.id
        }
      })
      if (!contact) {
        // Create a new contact
        contact = await prisma.contact.create({
          data: {
            email: primaryRecipientEmail,
            userId: user.id,
            firstName: to[0]?.name || "",
            lastName: ""
          }
        })
      }
    }

    // Check and reset daily quota if needed
    let currentDailyCount = user.dailySendCount
    if (shouldResetDailyCount(user.lastSendReset)) {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          dailySendCount: 0,
          lastSendReset: new Date()
        }
      })
      currentDailyCount = 0
    }

    if (currentDailyCount >= user.dailySendLimit) {
      throw new Error(`Daily send limit of ${user.dailySendLimit} emails exceeded. Try again tomorrow.`)
    }

    // Generate unique tracking ID
    const trackingId = `track_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create initial email tracking record
    const sentEmailRecord = await prisma.sentEmail.create({
      data: {
        campaignId: campaignId || null,
        contactId: contact?.id || '',
        status: "PENDING",
        trackingId: trackingId,
        gmailMessageId: null
      }
    })

    try {
      // Send the email
      const result = await sendEmail(user.id, {
        to,
        subject,
        htmlBody,
        textBody,
        trackingId,
        attachments
      })

      if (result.success) {
        // Update email tracking record with success
        await prisma.sentEmail.update({
          where: { id: sentEmailRecord.id },
          data: {
            status: "SENT",
            gmailMessageId: result.messageId,
            sentAt: new Date()
          }
        })

        // Update daily send count
        await prisma.user.update({
          where: { id: user.id },
          data: {
            dailySendCount: currentDailyCount + 1
          }
        })

        // End performance monitoring
        const duration = endPerformanceMetric('gmail-send-email')
        if (duration && duration > 5000) {
          console.warn(`Slow email send: ${duration.toFixed(2)}ms for user ${user.id}`)
        }

        return {
          success: true,
          messageId: result.messageId,
          trackingId: trackingId,
          sentEmailId: sentEmailRecord.id,
          message: "Email sent successfully"
        }
      } else {
        // Update email tracking record with failure
        await prisma.sentEmail.update({
          where: { id: sentEmailRecord.id },
          data: {
            status: "FAILED",
            errorMessage: result.error || "Unknown error occurred"
          }
        })

        endPerformanceMetric('gmail-send-email')
        throw new Error(result.error || "Failed to send email")
      }
    } catch (error) {
      // Update email tracking record with failure if not already updated
      await prisma.sentEmail.update({
        where: { id: sentEmailRecord.id },
        data: {
          status: "FAILED",
          errorMessage: error instanceof Error ? error.message : "Unknown error occurred"
        }
      })

      endPerformanceMetric('gmail-send-email')
      throw error
    }
  })
}