/**
 * App Preloader Component
 * Handles automatic preloading of all app data with visual feedback
 */

'use client'

import React, { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useAppPreloader, usePreloadMetrics } from '@/hooks/useAppPreloader'
import { PreloadSummary } from '@/lib/preloader/appPreloader'

interface AppPreloaderProps {
  showProgress?: boolean
  showMetrics?: boolean
  priority?: 'high' | 'medium' | 'low'
  autoStart?: boolean
  onComplete?: (summary: PreloadSummary) => void
  className?: string
}

export function AppPreloader({
  showProgress = true,
  showMetrics = false,
  priority = 'high',
  autoStart = true,
  onComplete,
  className = ''
}: AppPreloaderProps) {
  console.log('🔧 [APP PRELOADER] Component mounting with props:', { showProgress, showMetrics, priority, autoStart })

  const { data: session } = useSession()
  const { metrics, updateMetrics } = usePreloadMetrics()
  const [showDetails, setShowDetails] = useState(false)

  console.log('🔧 [APP PRELOADER] Session data:', session?.user?.id ? 'User ID exists' : 'No user ID')

  const {
    isPreloading,
    preloadSummary,
    error,
    startPreload,
    progress
  } = useAppPreloader({
    autoPreload: autoStart,
    priority,
    modules: {
      gmail: true,
      calendar: true,
      aiAgent: true
    },
    timeRanges: {
      calendar: {
        pastDays: 0, // Use current month only like calendar page
        futureDays: 30 // Current month covers this
      },
      gmail: {
        maxEmails: 50, // Match EmailPageLayout default
        includeDrafts: true,
        includeSent: true
      }
    },
    onPreloadComplete: (summary) => {
      updateMetrics(summary)
      onComplete?.(summary)
    },
    onPreloadError: (error) => {
      console.error('Preload error:', error)
    }
  })

  // Don't render if no session
  if (!session?.user) {
    return null
  }

  // Don't render if not showing progress and not preloading
  if (!showProgress && !isPreloading) {
    return null
  }

  return (
    <div className={`app-preloader ${className}`}>
      {/* Preloading Indicator */}
      {isPreloading && showProgress && (
        <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 min-w-[300px]">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Preloading App Data...
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Preparing Gmail and Calendar data for instant access
              </div>
            </div>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400"
            >
              {showDetails ? 'Hide' : 'Details'}
            </button>
          </div>

          {/* Progress Details */}
          {showDetails && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Queue Size:</span>
                  <span className="font-mono">{progress.queueSize}</span>
                </div>
                <div className="flex justify-between">
                  <span>Priority:</span>
                  <span className="capitalize font-medium">{priority}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Completion Summary */}
      {preloadSummary && !isPreloading && showProgress && (
        <div className="fixed top-4 right-4 z-50 bg-green-50 dark:bg-green-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-800 p-4 min-w-[300px]">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-green-900 dark:text-green-100">
                App Data Preloaded Successfully!
              </div>
              <div className="text-xs text-green-700 dark:text-green-300 mt-1">
                {preloadSummary.successCount} items loaded in {preloadSummary.totalTime}ms
                {preloadSummary.cacheHitRate > 0 && (
                  <span className="ml-2">
                    ({(preloadSummary.cacheHitRate * 100).toFixed(0)}% from cache)
                  </span>
                )}
              </div>
              
              {showDetails && (
                <div className="mt-2 pt-2 border-t border-green-200 dark:border-green-700">
                  <div className="text-xs space-y-1">
                    {preloadSummary.results.map((result, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="flex items-center space-x-1">
                          <span className={`w-2 h-2 rounded-full ${
                            result.success ? 'bg-green-500' : 'bg-red-500'
                          }`}></span>
                          <span>{result.module}:{result.dataType}</span>
                        </span>
                        <span className="font-mono text-xs">
                          {result.loadTime}ms
                          {result.cached && <span className="ml-1 text-blue-600">📋</span>}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs text-green-600 hover:text-green-800 dark:text-green-400"
            >
              {showDetails ? 'Hide' : 'Details'}
            </button>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && showProgress && (
        <div className="fixed top-4 right-4 z-50 bg-red-50 dark:bg-red-900/20 rounded-lg shadow-lg border border-red-200 dark:border-red-800 p-4 min-w-[300px]">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-red-900 dark:text-red-100">
                Preload Error
              </div>
              <div className="text-xs text-red-700 dark:text-red-300 mt-1">
                {error.message}
              </div>
              <button
                onClick={startPreload}
                className="mt-2 text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {showMetrics && (
        <div className="fixed bottom-4 right-4 z-40 bg-gray-50 dark:bg-gray-800 rounded-lg shadow-lg border p-3 text-xs">
          <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">
            Preload Metrics
          </div>
          <div className="space-y-1 text-gray-600 dark:text-gray-400">
            <div className="flex justify-between">
              <span>Total Preloads:</span>
              <span className="font-mono">{metrics.totalPreloads}</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Load Time:</span>
              <span className="font-mono">{metrics.averageLoadTime.toFixed(0)}ms</span>
            </div>
            <div className="flex justify-between">
              <span>Cache Hit Rate:</span>
              <span className="font-mono">{(metrics.cacheHitRate * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span>Error Rate:</span>
              <span className="font-mono">{(metrics.errorRate * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Minimal preloader for production use
export function MinimalAppPreloader({ 
  priority = 'high',
  onComplete 
}: { 
  priority?: 'high' | 'medium' | 'low'
  onComplete?: (summary: PreloadSummary) => void 
}) {
  useAppPreloader({
    autoPreload: true,
    priority,
    modules: {
      gmail: true,
      calendar: true,
      aiAgent: true
    },
    onPreloadComplete: onComplete
  })

  return null // No UI, just preloading logic
}

// Preloader status indicator (small, unobtrusive)
export function PreloaderStatusIndicator() {
  const { data: session } = useSession()
  const { isPreloading } = useAppPreloader({ autoPreload: false })

  if (!session?.user || !isPreloading) {
    return null
  }

  return (
    <div className="fixed top-2 right-2 z-50">
      <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
        <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
        <span>Loading...</span>
      </div>
    </div>
  )
}
