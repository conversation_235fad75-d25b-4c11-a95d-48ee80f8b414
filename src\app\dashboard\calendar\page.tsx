'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { CalendarView } from '@/components/calendar/calendarView'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Plus, 
  Filter,
  Calendar as CalendarIcon,
  Settings,
  Share2,
  Palette,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { CalendarStats, GoogleCalendarEvent } from '@/lib/calendar-types'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

interface CalendarData {
  id: string
  summary: string
  description?: string
  primary?: boolean
  accessRole: string
  selected?: boolean
  backgroundColor?: string
  foregroundColor?: string
}

interface CalendarColors {
  calendar: Record<string, { background: string; foreground: string }>
  event: Record<string, { background: string; foreground: string }>
}

interface CalendarSettings {
  weekStart: number
  showWeekNumbers: boolean
  defaultEventLength: number
  workingHours: {
    start: string
    end: string
  }
  timeZone: string
}

export default function CalendarPage() {
  const { data: session } = useSession()
  const router = useRouter()
  
  const [events, setEvents] = useState<GoogleCalendarEvent[]>([])
  const [calendars, setCalendars] = useState<CalendarData[]>([])
  const [colors, setColors] = useState<CalendarColors>({ calendar: {}, event: {} })
  const [settings, setSettings] = useState<CalendarSettings>({
    weekStart: 0,
    showWeekNumbers: false,
    defaultEventLength: 60,
    workingHours: { start: '09:00', end: '17:00' },
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone // Use user's timezone
  })
  const [stats, setStats] = useState<CalendarStats>({
    totalEvents: 0,
    thisWeekEvents: 0,
    upcomingEvents: 0,
    calendarsCount: 0
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedView, setSelectedView] = useState<'Month' | 'Week' | 'Day'>('Month')
  const [currentDate, setCurrentDate] = useState(new Date())
  const [needsConnection, setNeedsConnection] = useState(false)
  const [debugMode, setDebugMode] = useState(false)

  // Get user's timezone
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone

  // Use ref to track loading state to prevent race conditions
  const isLoadingRef = useRef(false)

  // Debounced calendar data loading
  const loadCalendarData = useCallback(async (dateToLoad?: Date) => {
    const targetDate = dateToLoad || currentDate
    console.log('🗓️ [CALENDAR PAGE] loadCalendarData called - user ID:', session?.user?.id, 'date:', targetDate)

    if (!session?.user?.id) {
      console.log('loadCalendarData - no user ID, returning')
      return
    }

    // Prevent multiple simultaneous calls
    if (isLoadingRef.current) {
      console.log('loadCalendarData - already loading, returning')
      return
    }

    console.log('loadCalendarData - starting API call')
    isLoadingRef.current = true
    setLoading(true)
    setError(null)
    setNeedsConnection(false)

    try {
      const startOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1)
      const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0)

      const params = new URLSearchParams({
        timeMin: startOfMonth.toISOString(),
        timeMax: endOfMonth.toISOString(),
        maxResults: '50'
      })

      console.log('loadCalendarData - making fetch request to:', `/api/calendar?${params}`)
      const response = await fetch(`/api/calendar?${params}`)

      console.log('loadCalendarData - response status:', response.status)

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // User needs to connect calendar
          console.log('loadCalendarData - needs connection')
          setNeedsConnection(true)
          return
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('loadCalendarData - response data:', data)

      if (data.error) {
        if (data.error.includes('not connected') || data.error.includes('unauthorized')) {
          // User needs to connect calendar
          console.log('loadCalendarData - needs connection (from error)')
          setNeedsConnection(true)
          return
        }
        throw new Error(data.error)
      }

      console.log('loadCalendarData - setting events:', data.events?.length || 0)
      setEvents(data.events || [])
      setStats(data.stats || { totalEvents: 0, upcomingEvents: 0, todayEvents: 0, calendarsCount: 0 })
    } catch (error) {
      console.error('Error loading calendar data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load calendar data')
    } finally {
      console.log('loadCalendarData - finished, setting loading to false')
      setLoading(false)
      isLoadingRef.current = false
    }
  }, [session?.user?.id]) // Remove currentDate to prevent infinite loop

  // Single effect to handle calendar data loading
  useEffect(() => {
    console.log('🗓️ [CALENDAR PAGE] Calendar effect - session status:', session === undefined ? 'loading' : session === null ? 'no session' : 'session exists')
    console.log('🗓️ [CALENDAR PAGE] Calendar effect - user ID:', session?.user?.id)
    console.log('🗓️ [CALENDAR PAGE] Calendar effect - currentDate:', currentDate)

    if (session === undefined) return // Still loading session

    if (session?.user?.id) {
      console.log('🗓️ [CALENDAR PAGE] Calendar effect - calling loadCalendarData with currentDate')
      loadCalendarData(currentDate)
    }
  }, [session?.user?.id, currentDate]) // Remove loadCalendarData from dependencies

  // Component mount/unmount logging
  useEffect(() => {
    console.log('🗓️ [CALENDAR PAGE] Calendar page component MOUNTED')
    return () => {
      console.log('🗓️ [CALENDAR PAGE] Calendar page component UNMOUNTED')
    }
  }, [])

  const handleRetry = () => {
    loadCalendarData(currentDate)
  }

  const handleCreateEvent = async (eventData: Partial<GoogleCalendarEvent>) => {
    try {
      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...eventData,
          timeZone: userTimezone // Always use user's timezone
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create event')
      }
      
      // Reload events after creating
      loadCalendarData(currentDate)
    } catch (err) {
      console.error('Error creating event:', err)
      setError(err instanceof Error ? err.message : 'Failed to create event')
    }
  }

  const handleUpdateEvent = async (eventId: string, eventData: Partial<GoogleCalendarEvent>) => {
    try {
      const response = await fetch(`/api/calendar?eventId=${eventId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...eventData,
          timeZone: userTimezone // Always use user's timezone
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to update event')
      }
      
      // Reload events after updating
      loadCalendarData(currentDate)
    } catch (err) {
      console.error('Error updating event:', err)
      setError(err instanceof Error ? err.message : 'Failed to update event')
    }
  }

  const handleDeleteEvent = async (eventId: string) => {
    try {
      const response = await fetch(`/api/calendar?eventId=${eventId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete event')
      }
      
      // Reload events after deleting
      loadCalendarData(currentDate)
    } catch (err) {
      console.error('Error deleting event:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete event')
    }
  }

  const handleCalendarToggle = (calendarId: string) => {
    setCalendars(prev => prev.map(cal => 
      cal.id === calendarId ? { ...cal, selected: !cal.selected } : cal
    ))
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }



  const handleNewEvent = () => {
    router.push('/dashboard/calendar/new-event')
  }

  // Filter events based on search query
  const filteredEvents = events.filter(event => 
    event.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.location?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const monthYear = currentDate.toLocaleDateString('en-US', { 
    month: 'long', 
    year: 'numeric' 
  })

  if (needsConnection) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <CalendarIcon className="h-16 w-16 text-blue-500 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Connect Your Google Calendar</h2>
          <p className="text-muted-foreground mb-6">
            To use the smart calendar features, you need to connect your Google Calendar account.
          </p>
          <Button onClick={() => router.push('/api/calendar/connect')} className="mb-4">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Connect Google Calendar
          </Button>
          <p className="text-xs text-muted-foreground">
            This will allow you to view, create, and manage your calendar events.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading calendar...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Calendar Error</h2>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-80 border-r bg-card p-6 overflow-y-auto">
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <CalendarIcon className="h-6 w-6 text-primary" />
              <h1 className="text-xl font-semibold">Calendar</h1>
            </div>
            <p className="text-sm text-muted-foreground">Smart calendar management</p>
            <p className="text-xs text-muted-foreground mt-1">Timezone: {userTimezone}</p>
          </div>

          {/* Create Event Button */}
          <Button 
            className="w-full"
            onClick={handleNewEvent}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create
          </Button>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search events..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Mini Calendar Navigation */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="font-medium">{monthYear}</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* View Selector */}
            <div className="flex gap-1 p-1 bg-muted rounded-lg">
              {(['Month', 'Week', 'Day'] as const).map((view) => (
                <Button
                  key={view}
                  variant={selectedView === view ? 'default' : 'ghost'}
                  size="sm"
                  className="flex-1"
                  onClick={() => setSelectedView(view)}
                >
                  {view}
                </Button>
              ))}
            </div>
          </div>

          {/* My Calendars */}
          <div className="space-y-3">
            <h3 className="font-medium">My Calendars</h3>
            {calendars.map((calendar) => (
              <div key={calendar.id} className="flex items-center space-x-2">
                <Checkbox
                  id={calendar.id}
                  checked={calendar.selected !== false}
                  onCheckedChange={() => handleCalendarToggle(calendar.id)}
                />
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: calendar.backgroundColor || '#1976d2' }}
                />
                <label 
                  htmlFor={calendar.id}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                >
                  {calendar.summary}
                </label>
                {calendar.primary && (
                  <Badge variant="secondary" className="text-xs">
                    Primary
                  </Badge>
                )}
              </div>
            ))}
          </div>

          {/* Quick Stats */}
          <div className="space-y-3">
            <h3 className="font-medium">Quick Stats</h3>
            <div className="grid grid-cols-2 gap-3">
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalEvents}</div>
                  <div className="text-xs text-muted-foreground">Total Events</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-3">
                  <div className="text-2xl font-bold text-green-600">{stats.thisWeekEvents}</div>
                  <div className="text-xs text-muted-foreground">This Week</div>
                </CardContent>
              </Card>
            </div>
          </div>

          <Separator />

          {/* Quick Actions */}
          <div className="space-y-2">
            <Button variant="ghost" className="w-full justify-start">
              <Palette className="h-4 w-4 mr-2" />
              Customize Colors
            </Button>
            <Button variant="ghost" className="w-full justify-start">
              <Share2 className="h-4 w-4 mr-2" />
              Share Calendar
            </Button>
            <Button variant="ghost" className="w-full justify-start">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b bg-card px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => setCurrentDate(new Date())}>
                Today
              </Button>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateMonth('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateMonth('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <h2 className="text-xl font-semibold">{monthYear}</h2>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm" onClick={() => loadCalendarData(currentDate)}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Load Calendar
              </Button>
              <Button variant="outline" size="sm" onClick={() => setDebugMode(!debugMode)}>
                Debug
              </Button>
              <Button size="sm" onClick={handleNewEvent}>
                <Plus className="h-4 w-4 mr-2" />
                Create
              </Button>
            </div>
          </div>
        </div>

        {/* Debug Panel */}
        {debugMode && (
          <div className="border-b bg-yellow-50 p-4 text-sm">
            <strong>Debug Info:</strong>
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <strong>Loading:</strong> {loading ? 'Yes' : 'No'}<br/>
                <strong>Error:</strong> {error || 'None'}<br/>
                <strong>Needs Connection:</strong> {needsConnection ? 'Yes' : 'No'}<br/>
                <strong>Events Count:</strong> {events.length}<br/>
              </div>
              <div>
                <strong>Session:</strong> {session ? 'Exists' : 'None'}<br/>
                <strong>User ID:</strong> {session?.user?.id || 'None'}<br/>
                <strong>Calendar Connected:</strong> {session?.user?.calendarConnected ? 'Yes' : 'No'}<br/>
                <strong>Current Date:</strong> {currentDate.toISOString()}<br/>
              </div>
            </div>
          </div>
        )}

        {/* Calendar Content */}
        <div className="flex-1 overflow-hidden">
          <CalendarView
            events={filteredEvents}
            calendars={calendars}
            colors={colors}
            settings={settings}
            stats={stats}
            onEventCreate={handleCreateEvent}
            onEventUpdate={handleUpdateEvent}
            onEventDelete={handleDeleteEvent}
            onCalendarToggle={handleCalendarToggle}

            currentDate={currentDate}
            view={selectedView}
            userTimezone={userTimezone}
            loading={loading}
            error={error}
            needsConnection={needsConnection}
            onRetry={handleRetry}
          />
        </div>
      </div>
    </div>
  )
} 